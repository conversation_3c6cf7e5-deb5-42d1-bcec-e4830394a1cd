{% extends 'base.html' %}

{% block title %}Přehled - FinančníZdraví{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">P<PERSON><PERSON><PERSON></h1>

    <div class="row">
        <!-- Sk<PERSON>re finančního zdraví -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Skóre finančního zdraví</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ score }}/100</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-heart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Měsíční příjem -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Měsíční příjem</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ finances.income|round(2) }} Kč</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Měsíční výdaje -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Měsíční výdaje</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ finances.expenses|round(2) }} Kč</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-credit-card fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Čisté úspory -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Čisté úspory</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ finances.net|round(2) }} Kč</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-piggy-bank fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Graf výdajů podle kategorií -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Výdaje podle kategorií</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:300px;">
                        <canvas id="spendingChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Graf příjmů vs výdajů -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Příjmy vs výdaje</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:300px;">
                        <canvas id="incomeExpensesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Doporučení -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Finanční doporučení</h6>
                </div>
                <div class="card-body">
                    {% if recommendations %}
                        {% for rec in recommendations %}
                            <div class="recommendation {{ rec.type }}">
                                <h5>{{ rec.title }}</h5>
                                <p>{{ rec.description }}</p>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p>Žádná doporučení nejsou k dispozici. Přidejte více finančních dat pro získání personalizovaných rad.</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Poslední transakce -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Poslední transakce</h6>
                    <a href="{{ url_for('main.transactions') }}" class="btn btn-sm btn-primary">Zobrazit vše</a>
                </div>
                <div class="card-body">
                    {% if transactions %}
                        {% for transaction in transactions %}
                            <div class="transaction-item">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <strong>{{ transaction.description or 'Bez popisu' }}</strong>
                                        <div class="text-muted small">{{ transaction.category }}</div>
                                    </div>
                                    <div class="text-{% if transaction.amount < 0 %}danger{% else %}success{% endif %}">
                                        {{ transaction.amount|abs|round(2) }} Kč
                                    </div>
                                </div>
                                <div class="text-muted small">{{ transaction.transaction_date.strftime('%d.%m.%Y') }}</div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p>Žádné nedávné transakce. <a href="{{ url_for('main.transactions') }}">Přidejte nějaké transakce</a> pro začátek.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Spořící cíle -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Spořící cíle</h6>
                    <a href="{{ url_for('main.goals') }}" class="btn btn-sm btn-primary">Spravovat cíle</a>
                </div>
                <div class="card-body">
                    {% if goals %}
                        <div class="row">
                            {% for goal in goals %}
                                <div class="col-lg-4 col-md-6 mb-4">
                                    <div class="card border-left-info shadow h-100">
                                        <div class="card-body">
                                            <div class="row no-gutters align-items-center">
                                                <div class="col mr-2">
                                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                        {{ goal.name }}</div>
                                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ goal.current_amount|round(2) }} Kč / {{ goal.target_amount|round(2) }} Kč</div>
                                                    <div class="progress progress-sm mt-2">
                                                        <div class="progress-bar bg-info" role="progressbar"
                                                            style="width: {{ (goal.current_amount / goal.target_amount * 100)|round|int if goal.target_amount > 0 else 0 }}%"
                                                            aria-valuenow="{{ (goal.current_amount / goal.target_amount * 100)|round|int if goal.target_amount > 0 else 0 }}"
                                                            aria-valuemin="0"
                                                            aria-valuemax="100"></div>
                                                    </div>
                                                    {% if goal.target_date %}
                                                        <div class="text-muted small mt-1">Cílové datum: {{ goal.target_date.strftime('%d.%m.%Y') }}</div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p>Nemáte nastavené žádné spořící cíle. <a href="{{ url_for('main.goals') }}">Vytvořte si spořící cíl</a> pro sledování vašeho pokroku.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Graf výdajů podle kategorií
    const spendingCtx = document.getElementById('spendingChart').getContext('2d');
    const spendingData = {
        labels: [{% for category, amount in spending.items() %}'{{ category }}',{% endfor %}],
        datasets: [{
            data: [{% for category, amount in spending.items() %}{{ amount }},{% endfor %}],
            backgroundColor: [
                '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b',
                '#6f42c1', '#fd7e14', '#20c9a6', '#5a5c69', '#858796'
            ],
            hoverBackgroundColor: [
                '#2e59d9', '#17a673', '#2c9faf', '#dda20a', '#be2617',
                '#5a30a3', '#d56308', '#169b80', '#3a3b45', '#60616f'
            ],
            hoverBorderColor: "rgba(234, 236, 244, 1)",
        }]
    };
    const spendingChart = new Chart(spendingCtx, {
        type: 'doughnut',
        data: spendingData,
        options: {
            maintainAspectRatio: false,
            tooltips: {
                callbacks: {
                    label: function(tooltipItem, data) {
                        const dataset = data.datasets[tooltipItem.datasetIndex];
                        const currentValue = dataset.data[tooltipItem.index];
                        return `${data.labels[tooltipItem.index]}: ${currentValue.toFixed(2)} Kč`;
                    }
                }
            },
            legend: {
                position: 'right',
                labels: {
                    boxWidth: 12
                }
            },
            cutoutPercentage: 70,
        },
    });

    // Graf příjmů vs výdajů
    const incomeExpensesCtx = document.getElementById('incomeExpensesChart').getContext('2d');
    const incomeExpensesData = {
        labels: ['Příjmy', 'Výdaje'],
        datasets: [{
            data: [{{ finances.income }}, {{ finances.expenses }}],
            backgroundColor: ['#1cc88a', '#e74a3b'],
            hoverBackgroundColor: ['#17a673', '#be2617'],
            hoverBorderColor: "rgba(234, 236, 244, 1)",
        }]
    };
    const incomeExpensesChart = new Chart(incomeExpensesCtx, {
        type: 'bar',
        data: incomeExpensesData,
        options: {
            maintainAspectRatio: false,
            scales: {
                yAxes: [{
                    ticks: {
                        beginAtZero: true,
                        callback: function(value) {
                            return value + ' Kč';
                        }
                    }
                }]
            },
            tooltips: {
                callbacks: {
                    label: function(tooltipItem, data) {
                        return tooltipItem.yLabel.toFixed(2) + ' Kč';
                    }
                }
            },
            legend: {
                display: false
            }
        }
    });
</script>
{% endblock %}
