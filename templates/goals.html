{% extends 'base.html' %}

{% block title %}Savings Goals - FinHealth{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Savings Goals</h1>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Add New Savings Goal</h6>
                    <button class="btn btn-sm btn-primary" type="button" data-bs-toggle="collapse" data-bs-target="#addGoalForm">
                        <i class="fas fa-plus"></i> Add Goal
                    </button>
                </div>
                <div class="card-body collapse" id="addGoalForm">
                    <form method="POST" action="{{ url_for('main.add_goal') }}">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="name" class="form-label">Goal Name</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label for="target_amount" class="form-label">Target Amount</label>
                                <input type="number" step="0.01" min="0" class="form-control" id="target_amount" name="target_amount" required>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label for="current_amount" class="form-label">Current Amount</label>
                                <input type="number" step="0.01" min="0" class="form-control" id="current_amount" name="current_amount" value="0">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="target_date" class="form-label">Target Date (Optional)</label>
                                <input type="date" class="form-control" id="target_date" name="target_date">
                            </div>
                            <div class="col-md-2 mb-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">Save Goal</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        {% if goals %}
            {% for goal in goals %}
                <div class="col-xl-4 col-md-6 mb-4">
                    <div class="card shadow h-100">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">{{ goal.name }}</h6>
                            <div class="dropdown no-arrow">
                                <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink{{ goal.id }}" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                                </a>
                                <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink{{ goal.id }}">
                                    <div class="dropdown-header">Goal Actions:</div>
                                    <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#updateGoalModal{{ goal.id }}">
                                        <i class="fas fa-edit fa-sm fa-fw mr-2 text-gray-400"></i>
                                        Update Progress
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">${{ goal.current_amount|round(2) }} / ${{ goal.target_amount|round(2) }}</div>
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1 mt-2">
                                        Progress: {{ ((goal.current_amount / goal.target_amount) * 100)|round|int if goal.target_amount > 0 else 0 }}%
                                    </div>
                                    <div class="progress progress-sm mb-2">
                                        <div class="progress-bar bg-info" role="progressbar"
                                            style="width: {{ (goal.current_amount / goal.target_amount * 100)|round|int if goal.target_amount > 0 else 0 }}%"
                                            aria-valuenow="{{ (goal.current_amount / goal.target_amount * 100)|round|int if goal.target_amount > 0 else 0 }}"
                                            aria-valuemin="0"
                                            aria-valuemax="100"></div>
                                    </div>

                                    {% if goal.target_date %}
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                                            Target Date: {{ goal.target_date.strftime('%Y-%m-%d') }}
                                        </div>

                                        {% set days_left = (goal.target_date - datetime.now()).days %}
                                        {% if days_left > 0 %}
                                            <div class="text-xs mb-2">{{ days_left }} days remaining</div>

                                            {% set amount_left = goal.target_amount - goal.current_amount %}
                                            {% if amount_left > 0 %}
                                                <div class="text-xs mb-2">
                                                    Need to save ${{ (amount_left / days_left)|round(2) }} per day
                                                </div>
                                            {% endif %}
                                        {% elif days_left == 0 %}
                                            <div class="text-xs mb-2 text-warning">Due today!</div>
                                        {% else %}
                                            <div class="text-xs mb-2 text-danger">Past due by {{ days_left|abs }} days</div>
                                        {% endif %}
                                    {% endif %}

                                    <div class="text-xs text-muted">
                                        Created: {{ goal.created_at.strftime('%Y-%m-%d') }}
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-piggy-bank fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Update Goal Modal -->
                <div class="modal fade" id="updateGoalModal{{ goal.id }}" tabindex="-1" aria-labelledby="updateGoalModalLabel{{ goal.id }}" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="updateGoalModalLabel{{ goal.id }}">Update Goal: {{ goal.name }}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <form method="POST" action="{{ url_for('main.update_goal', goal_id=goal.id) }}">
                                <div class="modal-body">
                                    <div class="mb-3">
                                        <label for="current_amount{{ goal.id }}" class="form-label">Current Amount</label>
                                        <input type="number" step="0.01" min="0" class="form-control" id="current_amount{{ goal.id }}" name="current_amount" value="{{ goal.current_amount }}">
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="submit" class="btn btn-primary">Update Progress</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <div class="col-lg-12">
                <div class="card shadow mb-4">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-bullseye fa-3x text-gray-300 mb-3"></i>
                        <h5>No Savings Goals Yet</h5>
                        <p>Create your first savings goal to start tracking your progress towards financial milestones.</p>
                        <button class="btn btn-primary" type="button" data-bs-toggle="collapse" data-bs-target="#addGoalForm">
                            <i class="fas fa-plus"></i> Create Your First Goal
                        </button>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>

    {% if goals %}
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Goals Overview</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:300px;">
                        <canvas id="goalsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    {% if goals %}
    document.addEventListener('DOMContentLoaded', function() {
        // Goals Progress Chart
        const goalsCtx = document.getElementById('goalsChart').getContext('2d');
        const goalsData = {
            labels: [{% for goal in goals %}'{{ goal.name }}',{% endfor %}],
            datasets: [
                {
                    label: 'Current Amount',
                    backgroundColor: 'rgba(54, 185, 204, 0.5)',
                    borderColor: 'rgba(54, 185, 204, 1)',
                    borderWidth: 1,
                    data: [{% for goal in goals %}{{ goal.current_amount }},{% endfor %}]
                },
                {
                    label: 'Target Amount',
                    backgroundColor: 'rgba(78, 115, 223, 0.5)',
                    borderColor: 'rgba(78, 115, 223, 1)',
                    borderWidth: 1,
                    data: [{% for goal in goals %}{{ goal.target_amount }},{% endfor %}]
                }
            ]
        };
        const goalsChart = new Chart(goalsCtx, {
            type: 'bar',
            data: goalsData,
            options: {
                maintainAspectRatio: false,
                scales: {
                    yAxes: [{
                        ticks: {
                            beginAtZero: true,
                            callback: function(value) {
                                return '$' + value;
                            }
                        }
                    }]
                },
                tooltips: {
                    callbacks: {
                        label: function(tooltipItem, data) {
                            return data.datasets[tooltipItem.datasetIndex].label + ': $' + tooltipItem.yLabel.toFixed(2);
                        }
                    }
                }
            }
        });
    });
    {% endif %}
</script>
{% endblock %}
