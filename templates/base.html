<!DOCTYPE html>
<html>
<head>
    <title>{% block title %}FinHealth - Česká spořitelna{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/csas-style.css') }}">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Top Navbar -->
    <nav class="navbar navbar-expand navbar-dark top-navbar">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <span style="font-weight: 700;">Finanční</span><span style="font-weight: 400;">Zdraví</span>
            </a>
            <ul class="navbar-nav ms-auto">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle user-dropdown" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> {{ user.username if user else 'Uživatel' }}
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                        <li><a class="dropdown-item" href="{{ url_for('main.profile') }}"><i class="fas fa-id-card text-primary me-2"></i>Profil</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}"><i class="fas fa-sign-out-alt text-primary me-2"></i>Odhlásit se</a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Sidebar -->
    <nav class="col-md-2 d-md-block sidebar">
        <div class="sidebar-sticky">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {% if request.path == url_for('main.dashboard') %}active{% endif %}" href="{{ url_for('main.dashboard') }}">
                        <i class="fas fa-tachometer-alt"></i> Přehled
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.path == url_for('main.transactions') %}active{% endif %}" href="{{ url_for('main.transactions') }}">
                        <i class="fas fa-exchange-alt"></i> Transakce
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.path == url_for('main.budget') %}active{% endif %}" href="{{ url_for('main.budget') }}">
                        <i class="fas fa-wallet"></i> Rozpočet
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.path == url_for('main.goals') %}active{% endif %}" href="{{ url_for('main.goals') }}">
                        <i class="fas fa-bullseye"></i> Spořící cíle
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.path == url_for('main.profile') %}active{% endif %}" href="{{ url_for('main.profile') }}">
                        <i class="fas fa-user"></i> Profil
                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="revolutDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="fas fa-credit-card"></i> Revolut
                    </a>
                    <div class="dropdown-menu" aria-labelledby="revolutDropdown">
                        <a class="dropdown-item {% if request.path == url_for('main.revolut_setup') %}active{% endif %}" href="{{ url_for('main.revolut_setup') }}">
                            <i class="fas fa-building"></i> Business API
                        </a>
                        <a class="dropdown-item {% if request.path == url_for('main.revolut_retail_setup') %}active{% endif %}" href="{{ url_for('main.revolut_retail_setup') }}">
                            <i class="fas fa-user"></i> Open Banking
                        </a>
                    </div>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
