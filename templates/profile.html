{% extends 'base.html' %}

{% block title %}Profile - FinHealth{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">User Profile</h1>
    
    <div class="row">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Account Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Username</label>
                        <input type="text" class="form-control" value="{{ user.username }}" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Email</label>
                        <input type="email" class="form-control" value="{{ user.email }}" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Account Created</label>
                        <input type="text" class="form-control" value="{{ user.created_at.strftime('%Y-%m-%d') }}" readonly>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">API Integration</h6>
                </div>
                <div class="card-body">
                    <p>Connect your financial accounts to automatically import transactions.</p>
                    
                    <div class="mb-3">
                        <label for="api_key" class="form-label">API Key</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="api_key" placeholder="Enter your API key">
                            <button class="btn btn-primary" type="button" onclick="setApiKey()">Save</button>
                        </div>
                        <small class="form-text text-muted">Your API key is stored securely and used only to fetch your transaction data.</small>
                    </div>
                    
                    <div class="mt-4">
                        <h6 class="font-weight-bold">Connected Services</h6>
                        <div class="list-group">
                            <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-university me-2"></i> Revolut
                                    <small class="d-block text-muted">Import your Revolut transactions</small>
                                </div>
                                <span class="badge bg-{% if session.get('api_key') %}success{% else %}secondary{% endif %} rounded-pill">
                                    {% if session.get('api_key') %}Connected{% else %}Not Connected{% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Financial Health History</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:300px;">
                        <canvas id="healthScoreChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function setApiKey() {
        const apiKey = document.getElementById('api_key').value;
        fetch('{{ url_for("main.set_api_key") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `api_key=${apiKey}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                alert('API key saved successfully');
                location.reload();
            }
        })
        .catch(error => {
            alert('Error saving API key');
            console.error('Error:', error);
        });
    }
    
    // Sample health score chart (would be populated with real data in production)
    document.addEventListener('DOMContentLoaded', function() {
        const healthScoreCtx = document.getElementById('healthScoreChart').getContext('2d');
        const healthScoreChart = new Chart(healthScoreCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                datasets: [{
                    label: 'Financial Health Score',
                    data: [65, 68, 72, 70, 75, 78],
                    backgroundColor: 'rgba(78, 115, 223, 0.05)',
                    borderColor: 'rgba(78, 115, 223, 1)',
                    pointBackgroundColor: 'rgba(78, 115, 223, 1)',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: 'rgba(78, 115, 223, 1)',
                    borderWidth: 2,
                    fill: true
                }]
            },
            options: {
                maintainAspectRatio: false,
                scales: {
                    yAxes: [{
                        ticks: {
                            min: 0,
                            max: 100,
                            stepSize: 20
                        }
                    }]
                },
                tooltips: {
                    callbacks: {
                        label: function(tooltipItem, data) {
                            return 'Score: ' + tooltipItem.yLabel;
                        }
                    }
                }
            }
        });
    });
</script>
{% endblock %}
