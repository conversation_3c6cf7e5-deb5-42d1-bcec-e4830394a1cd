{% extends "base.html" %}

{% block title %}Nastavení Revolut integrace{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Nastavení Revolut integrace</h1>
        <a href="{{ url_for('main.dashboard') }}" class="btn btn-secondary btn-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Zpět na přehled
        </a>
    </div>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="row">
        <!-- Nastavení účtu -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Nastavení Revolut účtu</h6>
                </div>
                <div class="card-body">
                    {% if user.revolut_enabled and user.revolut_account_id %}
                        <div class="alert alert-success" role="alert">
                            <i class="fas fa-check-circle"></i>
                            Revolut integrace je aktivní. Účet ID: <strong>{{ user.revolut_account_id }}</strong>
                            {% if user.revolut_last_sync %}
                                <br><small>Poslední synchronizace: {{ user.revolut_last_sync.strftime('%d.%m.%Y %H:%M') }}</small>
                            {% endif %}
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <form method="POST" action="{{ url_for('main.revolut_sync') }}" class="d-inline">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-sync"></i> Synchronizovat transakce
                                    </button>
                                </form>
                            </div>
                            <div class="col-md-6">
                                <form method="POST" action="{{ url_for('main.revolut_test') }}" class="d-inline">
                                    <button type="submit" class="btn btn-info">
                                        <i class="fas fa-plug"></i> Test připojení
                                    </button>
                                </form>
                            </div>
                        </div>
                    {% else %}
                        <p class="text-muted">
                            Revolut integrace umožňuje automatické načítání vašich transakcí z Revolut účtu.
                            Pro aktivaci je potřeba nastavit ID vašeho Revolut účtu.
                        </p>
                        
                        <form method="POST">
                            <div class="form-group">
                                <label for="revolut_account_id">ID Revolut účtu</label>
                                <input type="text" class="form-control" id="revolut_account_id" 
                                       name="revolut_account_id" 
                                       placeholder="Zadejte ID vašeho Revolut účtu"
                                       value="{{ user.revolut_account_id or '' }}" required>
                                <small class="form-text text-muted">
                                    ID účtu najdete v Revolut Business aplikaci v sekci Nastavení > API.
                                </small>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Uložit nastavení
                            </button>
                            
                            <button type="button" class="btn btn-info" onclick="testConnection()">
                                <i class="fas fa-plug"></i> Test připojení
                            </button>
                        </form>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Dostupné účty -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Dostupné Revolut účty</h6>
                </div>
                <div class="card-body">
                    {% if available_accounts %}
                        <p class="text-muted small mb-3">Následující účty jsou dostupné přes Revolut API:</p>
                        {% for account in available_accounts %}
                            <div class="border rounded p-2 mb-2">
                                <strong>{{ account.name or 'Hlavní účet' }}</strong>
                                <br>
                                <small class="text-muted">
                                    ID: {{ account.id }}<br>
                                    Měna: {{ account.currency }}<br>
                                    Zůstatek: {{ account.balance }} {{ account.currency }}
                                </small>
                                {% if not user.revolut_account_id %}
                                    <br>
                                    <button type="button" class="btn btn-sm btn-outline-primary mt-1" 
                                            onclick="selectAccount('{{ account.id }}')">
                                        Vybrat tento účet
                                    </button>
                                {% endif %}
                            </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            Nejsou dostupné žádné účty. Zkontrolujte nastavení Revolut API.
                        </p>
                    {% endif %}
                </div>
            </div>

            <!-- Nápověda -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Nápověda</h6>
                </div>
                <div class="card-body">
                    <h6>Jak nastavit Revolut API:</h6>
                    <ol class="small">
                        <li>Přihlaste se do <a href="https://business.revolut.com" target="_blank">Revolut Business</a></li>
                        <li>Přejděte do Nastavení > API</li>
                        <li>Vytvořte nový API certifikát</li>
                        <li>Zkopírujte ID účtu a vložte ho výše</li>
                        <li>Nastavte environment variables pro API credentials</li>
                    </ol>
                    
                    <h6 class="mt-3">Potřebné environment variables:</h6>
                    <ul class="small">
                        <li><code>REVOLUT_CLIENT_ID</code></li>
                        <li><code>REVOLUT_PRIVATE_KEY</code></li>
                        <li><code>REVOLUT_SANDBOX=true/false</code></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function selectAccount(accountId) {
    document.getElementById('revolut_account_id').value = accountId;
}

function testConnection() {
    // Vytvoříme form pro test připojení
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{{ url_for("main.revolut_test") }}';
    document.body.appendChild(form);
    form.submit();
}
</script>
{% endblock %}
