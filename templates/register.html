<!DOCTYPE html>
<html>
<head>
    <title>Registrace - FinančníZdraví</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/csas-style.css') }}">
    <style>
        body {
            background-color: var(--csas-gray);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Open Sans', Arial, sans-serif;
        }
        .register-container {
            max-width: 400px;
            width: 100%;
            padding: 20px;
        }
        .card {
            border-radius: 12px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
            border: none;
            overflow: hidden;
        }
        .card-header {
            background-color: var(--csas-blue);
            color: white;
            text-align: center;
            padding: 25px;
            border-radius: 12px 12px 0 0 !important;
            border: none;
        }
        .btn-register {
            background-color: var(--csas-blue);
            border-color: var(--csas-blue);
            border-radius: 30px;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        .btn-register:hover {
            background-color: var(--csas-blue-dark);
            border-color: var(--csas-blue-dark);
        }
        .form-control {
            border-radius: 8px;
            padding: 12px;
            border: 1px solid var(--csas-gray-light);
        }
        .form-control:focus {
            border-color: var(--csas-blue-light);
            box-shadow: 0 0 0 0.2rem rgba(13, 101, 217, 0.25);
        }
        .logo {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        .logo span {
            font-weight: 400;
        }
        .form-label {
            font-weight: 500;
            color: var(--csas-gray-dark);
        }
        .link-primary {
            color: var(--csas-blue) !important;
            text-decoration: none;
        }
        .link-primary:hover {
            text-decoration: underline;
        }
        .back-link {
            color: var(--csas-gray-medium) !important;
            text-decoration: none;
        }
        .back-link:hover {
            color: var(--csas-gray-dark) !important;
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="card">
            <div class="card-header">
                <div class="logo"><span style="font-weight: 700;">Finanční</span><span>Zdraví</span></div>
                <h4>Vytvořit účet</h4>
            </div>
            <div class="card-body p-4">
                {% with messages = get_flashed_messages() %}
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-danger">{{ message }}</div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <form method="POST" action="{{ url_for('auth.register') }}">
                    <div class="mb-3">
                        <label for="username" class="form-label">Uživatelské jméno</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">E-mail</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Heslo</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-register">Registrovat se</button>
                    </div>
                </form>

                <div class="text-center mt-4">
                    <p>Již máte účet? <a href="{{ url_for('auth.login') }}" class="link-primary">Přihlásit se</a></p>
                    <a href="/" class="back-link"><i class="fas fa-arrow-left me-1"></i> Zpět na úvodní stránku</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
