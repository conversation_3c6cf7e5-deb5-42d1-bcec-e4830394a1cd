{% extends "base.html" %}

{% block title %}Nastavení Revolut Open Banking{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Nastavení Revolut Open Banking</h1>
        <div>
            <a href="{{ url_for('main.revolut_setup') }}" class="btn btn-info btn-sm me-2">
                <i class="fas fa-building fa-sm"></i> Business API
            </a>
            <a href="{{ url_for('main.dashboard') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left fa-sm text-white-50"></i> Zpět na přehled
            </a>
        </div>
    </div>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="row">
        <!-- Nastavení Open Banking -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Revolut Open Banking (Osobní účty)</h6>
                </div>
                <div class="card-body">
                    {% if user.revolut_ob_enabled and user.revolut_ob_access_token %}
                        <div class="alert alert-success" role="alert">
                            <i class="fas fa-check-circle"></i>
                            Revolut Open Banking je aktivní.
                            <br><strong>Status:</strong> {{ user.revolut_ob_consent_status or 'Aktivní' }}
                            {% if user.revolut_ob_last_sync %}
                                <br><small>Poslední synchronizace: {{ user.revolut_ob_last_sync.strftime('%d.%m.%Y %H:%M') }}</small>
                            {% endif %}
                            {% if user.revolut_ob_token_expires_at %}
                                <br><small>Token vyprší: {{ user.revolut_ob_token_expires_at.strftime('%d.%m.%Y %H:%M') }}</small>
                            {% endif %}
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <form method="POST" action="{{ url_for('main.revolut_retail_sync') }}" class="d-inline">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-sync"></i> Synchronizovat transakce
                                    </button>
                                </form>
                            </div>
                            <div class="col-md-4">
                                <form method="POST" action="{{ url_for('main.revolut_retail_test') }}" class="d-inline">
                                    <button type="submit" class="btn btn-info">
                                        <i class="fas fa-plug"></i> Test připojení
                                    </button>
                                </form>
                            </div>
                            <div class="col-md-4">
                                <form method="POST" onsubmit="return confirm('Opravdu chcete deaktivovat Revolut Open Banking?')">
                                    <input type="hidden" name="action" value="disable">
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-times"></i> Deaktivovat
                                    </button>
                                </form>
                            </div>
                        </div>
                    {% else %}
                        <div class="alert alert-info" role="alert">
                            <i class="fas fa-info-circle"></i>
                            Revolut Open Banking není nastaven.
                        </div>
                        
                        <p class="text-muted">
                            Revolut Open Banking umožňuje bezpečné načítání vašich transakcí přímo z vašeho osobního Revolut účtu.
                            Používá standardní Open Banking protokol s bankovní úrovní zabezpečení.
                        </p>
                        
                        <div class="mb-3">
                            <h6>Výhody Revolut Open Banking:</h6>
                            <ul class="text-muted">
                                <li>Automatické načítání všech transakcí</li>
                                <li>Bezpečná autentifikace přes Revolut</li>
                                <li>Žádné sdílení hesel nebo API klíčů</li>
                                <li>Standardní Open Banking protokol</li>
                                <li>Přístup k více účtům (CZK, EUR, atd.)</li>
                            </ul>
                        </div>
                        
                        <form method="POST">
                            <input type="hidden" name="action" value="start_authorization">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-link"></i> Připojit Revolut účet
                            </button>
                        </form>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                Po kliknutí budete přesměrováni na Revolut, kde se přihlásíte a povolíte přístup k vašim účtům.
                            </small>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Informace a nápověda -->
        <div class="col-lg-4">
            <!-- Status připojení -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Status připojení</h6>
                </div>
                <div class="card-body">
                    {% if connection_status %}
                        <div class="text-success">
                            <i class="fas fa-check-circle"></i>
                            API je dostupné
                        </div>
                    {% else %}
                        <div class="text-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            API není dostupné (demo režim)
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Nápověda -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Jak to funguje</h6>
                </div>
                <div class="card-body">
                    <h6>Proces připojení:</h6>
                    <ol class="small">
                        <li>Klikněte na "Připojit Revolut účet"</li>
                        <li>Budete přesměrováni na Revolut</li>
                        <li>Přihlaste se do vašeho Revolut účtu</li>
                        <li>Povolte přístup k účtům a transakcím</li>
                        <li>Budete přesměrováni zpět do aplikace</li>
                        <li>Transakce se automaticky synchronizují</li>
                    </ol>
                    
                    <h6 class="mt-3">Bezpečnost:</h6>
                    <ul class="small">
                        <li>Používáme standardní Open Banking protokol</li>
                        <li>Vaše přihlašovací údaje zůstávají u Revolut</li>
                        <li>Přístup můžete kdykoli zrušit</li>
                        <li>Tokeny mají omezenou platnost</li>
                    </ul>
                </div>
            </div>

            <!-- Rozdíl mezi Business a Open Banking -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Business vs Open Banking</h6>
                </div>
                <div class="card-body">
                    <div class="small">
                        <strong>Revolut Business API:</strong>
                        <ul>
                            <li>Pro firemní účty</li>
                            <li>Vyžaduje API klíče</li>
                            <li>Přímý přístup k API</li>
                        </ul>
                        
                        <strong>Revolut Open Banking:</strong>
                        <ul>
                            <li>Pro osobní účty</li>
                            <li>Bezpečná OAuth autentifikace</li>
                            <li>Standardní bankovní protokol</li>
                        </ul>
                    </div>
                    
                    <div class="mt-2">
                        <a href="{{ url_for('main.revolut_setup') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-building"></i> Business API
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
