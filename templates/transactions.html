{% extends 'base.html' %}

{% block title %}Transactions - FinHealth{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Transactions</h1>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Add New Transaction</h6>
                    <button class="btn btn-sm btn-primary" type="button" data-bs-toggle="collapse" data-bs-target="#addTransactionForm">
                        <i class="fas fa-plus"></i> Add Transaction
                    </button>
                </div>
                <div class="card-body collapse" id="addTransactionForm">
                    <form method="POST" action="{{ url_for('main.add_transaction') }}">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="description" class="form-label">Description</label>
                                <input type="text" class="form-control" id="description" name="description" required>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label for="amount" class="form-label">Amount</label>
                                <input type="number" step="0.01" class="form-control" id="amount" name="amount" required>
                                <small class="form-text text-muted">Use negative values for expenses</small>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label for="category" class="form-label">Category</label>
                                <select class="form-select" id="category" name="category">
                                    <option value="">Auto-categorize</option>
                                    {% for category in categories %}
                                        <option value="{{ category.name }}">{{ category.name|capitalize }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label for="date" class="form-label">Date</label>
                                <input type="date" class="form-control" id="date" name="date" value="{{ datetime.now().strftime('%Y-%m-%d') }}" required>
                            </div>
                            <div class="col-md-2 mb-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">Save Transaction</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Transaction History</h6>
                    <a href="{{ url_for('main.import_transactions') }}" class="btn btn-sm btn-info">
                        <i class="fas fa-file-import"></i> Import Transactions
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="transactionsTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Description</th>
                                    <th>Category</th>
                                    <th>Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if transactions %}
                                    {% for transaction in transactions %}
                                        <tr>
                                            <td>{{ transaction.transaction_date.strftime('%Y-%m-%d') }}</td>
                                            <td>{{ transaction.description or 'No description' }}</td>
                                            <td>{{ transaction.category|capitalize }}</td>
                                            <td class="text-{% if transaction.amount < 0 %}danger{% else %}success{% endif %}">
                                                ${{ transaction.amount|abs|round(2) }}
                                                {% if transaction.amount < 0 %}
                                                    <i class="fas fa-arrow-down text-danger"></i>
                                                {% else %}
                                                    <i class="fas fa-arrow-up text-success"></i>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="4" class="text-center">No transactions found. Add your first transaction above.</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize datatable
        if (document.getElementById('transactionsTable')) {
            $('#transactionsTable').DataTable({
                order: [[0, 'desc']]
            });
        }
    });
</script>
{% endblock %}
