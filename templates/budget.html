{% extends 'base.html' %}

{% block title %}Budget - FinHealth{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Budget Management</h1>
    
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Add New Budget</h6>
                    <button class="btn btn-sm btn-primary" type="button" data-bs-toggle="collapse" data-bs-target="#addBudgetForm">
                        <i class="fas fa-plus"></i> Add Budget
                    </button>
                </div>
                <div class="card-body collapse" id="addBudgetForm">
                    <form method="POST" action="{{ url_for('main.add_budget') }}">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="category" class="form-label">Category</label>
                                <select class="form-select" id="category" name="category" required>
                                    {% for category in categories %}
                                        {% if category.type == 'expense' %}
                                            <option value="{{ category.name }}">{{ category.name|capitalize }}</option>
                                        {% endif %}
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="amount" class="form-label">Budget Amount</label>
                                <input type="number" step="0.01" min="0" class="form-control" id="amount" name="amount" required>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="period" class="form-label">Period</label>
                                <select class="form-select" id="period" name="period">
                                    <option value="monthly" selected>Monthly</option>
                                    <option value="weekly">Weekly</option>
                                    <option value="yearly">Yearly</option>
                                </select>
                            </div>
                            <div class="col-md-2 mb-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">Save Budget</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Budget Overview -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Budget Overview</h6>
                </div>
                <div class="card-body">
                    {% if budgets %}
                        <div class="chart-container" style="position: relative; height:300px;">
                            <canvas id="budgetChart"></canvas>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-wallet fa-3x text-gray-300 mb-3"></i>
                            <p>No budgets set up yet. Create your first budget to get started.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Budget vs Actual -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Budget vs Actual Spending</h6>
                </div>
                <div class="card-body">
                    {% if budgets %}
                        <div class="chart-container" style="position: relative; height:300px;">
                            <canvas id="budgetVsActualChart"></canvas>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-chart-bar fa-3x text-gray-300 mb-3"></i>
                            <p>No budget data available. Add budgets to see comparisons.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Your Budgets</h6>
                </div>
                <div class="card-body">
                    {% if budgets %}
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Category</th>
                                        <th>Budget Amount</th>
                                        <th>Period</th>
                                        <th>Current Spending</th>
                                        <th>Progress</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for budget in budgets %}
                                        <tr>
                                            <td>{{ budget.category|capitalize }}</td>
                                            <td>${{ budget.amount|round(2) }}</td>
                                            <td>{{ budget.period|capitalize }}</td>
                                            <td>
                                                {% if budget.category in spending %}
                                                    ${{ spending[budget.category]|round(2) }}
                                                {% else %}
                                                    $0.00
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="progress">
                                                    {% set percent = usage.get(budget.category, 0) %}
                                                    <div class="progress-bar {% if percent > 90 %}bg-danger{% elif percent > 70 %}bg-warning{% else %}bg-success{% endif %}" 
                                                         role="progressbar" 
                                                         style="width: {{ percent }}%" 
                                                         aria-valuenow="{{ percent }}" 
                                                         aria-valuemin="0" 
                                                         aria-valuemax="100">
                                                        {{ percent|round|int }}%
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <p>You haven't set up any budgets yet. Create your first budget to start tracking your spending.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    {% if budgets %}
    document.addEventListener('DOMContentLoaded', function() {
        // Budget Overview Chart
        const budgetCtx = document.getElementById('budgetChart').getContext('2d');
        const budgetData = {
            labels: [{% for budget in budgets %}'{{ budget.category|capitalize }}',{% endfor %}],
            datasets: [{
                data: [{% for budget in budgets %}{{ budget.amount }},{% endfor %}],
                backgroundColor: [
                    '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b',
                    '#6f42c1', '#fd7e14', '#20c9a6', '#5a5c69', '#858796'
                ],
                hoverBackgroundColor: [
                    '#2e59d9', '#17a673', '#2c9faf', '#dda20a', '#be2617',
                    '#5a30a3', '#d56308', '#169b80', '#3a3b45', '#60616f'
                ],
                hoverBorderColor: "rgba(234, 236, 244, 1)",
            }]
        };
        const budgetChart = new Chart(budgetCtx, {
            type: 'doughnut',
            data: budgetData,
            options: {
                maintainAspectRatio: false,
                tooltips: {
                    callbacks: {
                        label: function(tooltipItem, data) {
                            const dataset = data.datasets[tooltipItem.datasetIndex];
                            const currentValue = dataset.data[tooltipItem.index];
                            return `${data.labels[tooltipItem.index]}: $${currentValue.toFixed(2)}`;
                        }
                    }
                },
                legend: {
                    position: 'right',
                    labels: {
                        boxWidth: 12
                    }
                },
                cutoutPercentage: 70,
            },
        });

        // Budget vs Actual Chart
        const budgetVsActualCtx = document.getElementById('budgetVsActualChart').getContext('2d');
        const budgetVsActualData = {
            labels: [{% for budget in budgets %}'{{ budget.category|capitalize }}',{% endfor %}],
            datasets: [
                {
                    label: 'Budget',
                    backgroundColor: 'rgba(78, 115, 223, 0.5)',
                    borderColor: 'rgba(78, 115, 223, 1)',
                    borderWidth: 1,
                    data: [{% for budget in budgets %}{{ budget.amount }},{% endfor %}]
                },
                {
                    label: 'Actual',
                    backgroundColor: 'rgba(28, 200, 138, 0.5)',
                    borderColor: 'rgba(28, 200, 138, 1)',
                    borderWidth: 1,
                    data: [
                        {% for budget in budgets %}
                            {% if budget.category in spending %}
                                {{ spending[budget.category] }},
                            {% else %}
                                0,
                            {% endif %}
                        {% endfor %}
                    ]
                }
            ]
        };
        const budgetVsActualChart = new Chart(budgetVsActualCtx, {
            type: 'bar',
            data: budgetVsActualData,
            options: {
                maintainAspectRatio: false,
                scales: {
                    yAxes: [{
                        ticks: {
                            beginAtZero: true,
                            callback: function(value) {
                                return '$' + value;
                            }
                        }
                    }]
                },
                tooltips: {
                    callbacks: {
                        label: function(tooltipItem, data) {
                            return data.datasets[tooltipItem.datasetIndex].label + ': $' + tooltipItem.yLabel.toFixed(2);
                        }
                    }
                }
            }
        });
    });
    {% endif %}
</script>
{% endblock %}
