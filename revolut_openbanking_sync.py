"""
Synchronizace transakcí z Revolut Open Banking API (retail)
"""
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from models import db, Transaction, User
from revolut_openbanking_api import RevolutOpenBankingAPI
import logging

# Nastavení loggingu
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RevolutOpenBankingSyncService:
    """
    Služba pro synchronizaci transakcí z Revolut Open Banking API
    """
    
    def __init__(self):
        self.revolut_api = RevolutOpenBankingAPI()
    
    def test_connection(self) -> bool:
        """
        Otestuje připojení k Revolut Open Banking API
        """
        return self.revolut_api.test_connection()
    
    def create_consent_for_user(self, user_id: int) -> Dict:
        """
        Vytvoří consent pro uživatele
        
        Args:
            user_id: ID uživatele
        
        Returns:
            Consent data včetně ConsentId a authorization URL
        """
        try:
            user = User.query.get(user_id)
            if not user:
                return {'success': False, 'error': 'Uživatel nenalezen'}
            
            # Vytvoříme consent
            consent_data = self.revolut_api.create_account_access_consent()
            consent_id = consent_data['Data']['ConsentId']
            
            # Vytvoříme autorizační URL
            state = f"user_{user_id}_{consent_id}"
            auth_url = self.revolut_api.get_authorization_url(consent_id, state)
            
            # Uložíme consent ID k uživateli
            user.revolut_ob_consent_id = consent_id
            user.revolut_ob_consent_status = 'AwaitingAuthorisation'
            db.session.commit()
            
            logger.info(f"Consent vytvořen pro uživatele {user_id}: {consent_id}")
            
            return {
                'success': True,
                'consent_id': consent_id,
                'authorization_url': auth_url,
                'state': state
            }
            
        except Exception as e:
            logger.error(f"Chyba při vytváření consent pro uživatele {user_id}: {e}")
            return {'success': False, 'error': str(e)}
    
    def handle_authorization_callback(self, authorization_code: str, state: str) -> Dict:
        """
        Zpracuje callback z autorizace
        
        Args:
            authorization_code: Autorizační kód z callback
            state: State parameter z callback
        
        Returns:
            Výsledek zpracování
        """
        try:
            # Parsujeme state pro získání user_id
            if not state.startswith('user_'):
                return {'success': False, 'error': 'Neplatný state parameter'}
            
            parts = state.split('_')
            if len(parts) < 3:
                return {'success': False, 'error': 'Neplatný formát state'}
            
            user_id = int(parts[1])
            consent_id = parts[2]
            
            user = User.query.get(user_id)
            if not user:
                return {'success': False, 'error': 'Uživatel nenalezen'}
            
            # Vyměníme kód za tokeny
            token_data = self.revolut_api.exchange_code_for_token(authorization_code)
            
            # Uložíme tokeny k uživateli
            user.revolut_ob_access_token = token_data['access_token']
            user.revolut_ob_refresh_token = token_data.get('refresh_token')
            user.revolut_ob_consent_status = 'Authorised'
            user.revolut_ob_enabled = True
            user.revolut_ob_token_expires_at = datetime.now() + timedelta(seconds=token_data.get('expires_in', 3600))
            
            db.session.commit()
            
            logger.info(f"Autorizace dokončena pro uživatele {user_id}")
            
            return {
                'success': True,
                'user_id': user_id,
                'message': 'Autorizace byla úspěšná'
            }
            
        except Exception as e:
            logger.error(f"Chyba při zpracování callback: {e}")
            return {'success': False, 'error': str(e)}
    
    def sync_user_transactions(self, user_id: int, days_back: int = 30) -> Dict:
        """
        Synchronizuje transakce pro konkrétního uživatele
        
        Args:
            user_id: ID uživatele
            days_back: Počet dní zpět pro synchronizaci
        
        Returns:
            Slovník s výsledky synchronizace
        """
        try:
            user = User.query.get(user_id)
            if not user:
                return {'success': False, 'error': 'Uživatel nenalezen'}
            
            if not user.revolut_ob_enabled or not user.revolut_ob_access_token:
                return {'success': False, 'error': 'Revolut Open Banking není nastaveno'}
            
            # Nastavíme tokeny do API
            self.revolut_api.access_token = user.revolut_ob_access_token
            self.revolut_api.refresh_token = user.revolut_ob_refresh_token
            self.revolut_api.token_expires_at = user.revolut_ob_token_expires_at
            
            # Získáme účty
            accounts = self.revolut_api.get_accounts()
            
            if not accounts:
                return {'success': False, 'error': 'Žádné účty nenalezeny'}
            
            # Datum od kterého synchronizujeme
            from_date = datetime.now() - timedelta(days=days_back)
            to_date = datetime.now()
            
            total_new = 0
            total_updated = 0
            total_skipped = 0
            errors = []
            
            # Synchronizujeme transakce pro každý účet
            for account in accounts:
                account_id = account['AccountId']
                
                try:
                    transactions = self.revolut_api.get_account_transactions(
                        account_id, from_date, to_date
                    )
                    
                    result = self.process_transactions(transactions, user_id, account)
                    total_new += result['new_transactions']
                    total_updated += result['updated_transactions']
                    total_skipped += result['skipped_transactions']
                    errors.extend(result['errors'])
                    
                except Exception as e:
                    error_msg = f"Chyba při synchronizaci účtu {account_id}: {e}"
                    logger.error(error_msg)
                    errors.append(error_msg)
            
            # Aktualizujeme datum poslední synchronizace
            user.revolut_ob_last_sync = datetime.now()
            
            # Pokud se token obnovil, uložíme nový
            if (self.revolut_api.access_token and 
                self.revolut_api.access_token != user.revolut_ob_access_token):
                user.revolut_ob_access_token = self.revolut_api.access_token
                user.revolut_ob_token_expires_at = self.revolut_api.token_expires_at
            
            db.session.commit()
            
            result = {
                'success': True,
                'new_transactions': total_new,
                'updated_transactions': total_updated,
                'skipped_transactions': total_skipped,
                'total_processed': total_new + total_updated + total_skipped,
                'accounts_processed': len(accounts),
                'errors': errors
            }
            
            logger.info(f"Synchronizace dokončena pro uživatele {user_id}: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Chyba při synchronizaci transakcí pro uživatele {user_id}: {e}")
            return {'success': False, 'error': str(e)}
    
    def process_transactions(self, ob_transactions: List[Dict], user_id: int, account: Dict) -> Dict:
        """
        Zpracuje transakce z Open Banking API a uloží je do databáze
        
        Args:
            ob_transactions: Seznam transakcí z Open Banking API
            user_id: ID uživatele
            account: Informace o účtu
        
        Returns:
            Slovník s výsledky zpracování
        """
        new_transactions = 0
        updated_transactions = 0
        skipped_transactions = 0
        errors = []
        
        for ob_transaction in ob_transactions:
            try:
                # Zkontrolujeme, zda transakce již existuje
                external_id = ob_transaction['TransactionId']
                existing_transaction = Transaction.query.filter_by(
                    user_id=user_id,
                    external_id=external_id
                ).first()
                
                if existing_transaction:
                    # Transakce již existuje, zkontrolujeme, zda se změnila
                    if self.should_update_transaction(existing_transaction, ob_transaction):
                        self.update_transaction(existing_transaction, ob_transaction)
                        updated_transactions += 1
                    else:
                        skipped_transactions += 1
                else:
                    # Nová transakce
                    self.create_transaction(ob_transaction, user_id, account)
                    new_transactions += 1
                    
            except Exception as e:
                error_msg = f"Chyba při zpracování transakce {ob_transaction.get('TransactionId', 'unknown')}: {e}"
                logger.error(error_msg)
                errors.append(error_msg)
        
        # Uložíme změny do databáze
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            error_msg = f"Chyba při ukládání do databáze: {e}"
            logger.error(error_msg)
            errors.append(error_msg)
            return {'success': False, 'error': error_msg}
        
        return {
            'success': True,
            'new_transactions': new_transactions,
            'updated_transactions': updated_transactions,
            'skipped_transactions': skipped_transactions,
            'errors': errors
        }
    
    def should_update_transaction(self, existing_transaction: Transaction, ob_transaction: Dict) -> bool:
        """
        Zkontroluje, zda by měla být existující transakce aktualizována
        """
        # Zkontrolujeme stav transakce
        ob_status = ob_transaction.get('Status', '')
        
        # Pokud se stav změnil, aktualizujeme
        if existing_transaction.description.find(ob_status.lower()) == -1:
            return True
        
        return False
    
    def update_transaction(self, existing_transaction: Transaction, ob_transaction: Dict):
        """
        Aktualizuje existující transakci
        """
        status = ob_transaction.get('Status', '')
        if status:
            existing_transaction.description = f"{existing_transaction.description} ({status})"
        
        logger.info(f"Aktualizována transakce {existing_transaction.id}")
    
    def create_transaction(self, ob_transaction: Dict, user_id: int, account: Dict):
        """
        Vytvoří novou transakci v databázi
        """
        # Mapujeme transakci na lokální formát
        local_transaction_data = self.map_ob_transaction_to_local(ob_transaction, user_id, account)
        
        # Vytvoříme novou transakci
        transaction = Transaction(
            user_id=local_transaction_data['user_id'],
            amount=local_transaction_data['amount'],
            category=local_transaction_data['category'],
            description=local_transaction_data['description'],
            transaction_date=local_transaction_data['transaction_date'],
            external_id=local_transaction_data['external_id'],
            source=local_transaction_data['source'],
            currency=local_transaction_data['currency'],
            transaction_type=local_transaction_data['transaction_type']
        )
        
        db.session.add(transaction)
        logger.info(f"Vytvořena nová transakce z Revolut OB: {transaction.description}")
    
    def map_ob_transaction_to_local(self, ob_transaction: Dict, user_id: int, account: Dict) -> Dict:
        """
        Mapuje transakci z Open Banking API na lokální formát
        """
        # Získáme částku a směr
        amount_data = ob_transaction.get('Amount', {})
        amount = float(amount_data.get('Amount', 0))
        credit_debit = ob_transaction.get('CreditDebitIndicator', 'Debit')
        
        # Pokud je to výdaj, částka bude záporná
        if credit_debit == 'Debit':
            amount = -amount
        
        # Mapování kategorie
        category = self.map_ob_category(ob_transaction)
        
        # Popis transakce
        description = ob_transaction.get('TransactionInformation', '')
        merchant_details = ob_transaction.get('MerchantDetails', {})
        if merchant_details and merchant_details.get('MerchantName'):
            description = merchant_details['MerchantName']
        
        # Datum transakce
        booking_date = ob_transaction.get('BookingDateTime', '')
        if booking_date:
            transaction_date = datetime.fromisoformat(booking_date.replace('Z', '+00:00'))
        else:
            transaction_date = datetime.now()
        
        return {
            'user_id': user_id,
            'amount': amount,
            'category': category,
            'description': description,
            'transaction_date': transaction_date,
            'external_id': ob_transaction['TransactionId'],
            'source': 'revolut_openbanking',
            'currency': amount_data.get('Currency', 'CZK'),
            'transaction_type': 'income' if amount > 0 else 'expense'
        }
    
    def map_ob_category(self, ob_transaction: Dict) -> str:
        """
        Mapuje kategorii z Open Banking transakce na lokální kategorie
        """
        # Základní mapování podle typu a merchant informací
        merchant_details = ob_transaction.get('MerchantDetails', {})
        merchant_name = merchant_details.get('MerchantName', '').lower() if merchant_details else ''
        
        transaction_info = ob_transaction.get('TransactionInformation', '').lower()
        
        # Jednoduché mapování podle klíčových slov
        if any(word in merchant_name or word in transaction_info for word in ['tesco', 'albert', 'billa', 'lidl', 'kaufland']):
            return 'potraviny'
        elif any(word in merchant_name or word in transaction_info for word in ['mcdonald', 'kfc', 'restaurant', 'pizza']):
            return 'restaurace'
        elif any(word in merchant_name or word in transaction_info for word in ['shell', 'benzina', 'mol', 'uber', 'taxi']):
            return 'doprava'
        elif any(word in merchant_name or word in transaction_info for word in ['h&m', 'zara', 'reserved', 'oblečení']):
            return 'oblečení'
        elif any(word in merchant_name or word in transaction_info for word in ['alza', 'czc', 'mall', 'amazon']):
            return 'elektronika'
        elif any(word in merchant_name or word in transaction_info for word in ['lékárna', 'pharmacy', 'zdraví']):
            return 'zdraví'
        elif any(word in merchant_name or word in transaction_info for word in ['kino', 'cinema', 'netflix', 'spotify']):
            return 'zábava'
        else:
            return 'ostatní'


def setup_revolut_ob_for_user(user_id: int) -> Dict:
    """
    Zahájí proces nastavení Revolut Open Banking pro uživatele
    
    Args:
        user_id: ID uživatele
    
    Returns:
        Slovník s výsledkem a autorizační URL
    """
    try:
        sync_service = RevolutOpenBankingSyncService()
        result = sync_service.create_consent_for_user(user_id)
        
        if result['success']:
            logger.info(f"Revolut Open Banking nastaven pro uživatele {user_id}")
        
        return result
        
    except Exception as e:
        logger.error(f"Chyba při nastavování Revolut Open Banking pro uživatele {user_id}: {e}")
        return {'success': False, 'error': str(e)}
