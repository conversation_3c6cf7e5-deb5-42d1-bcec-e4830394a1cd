# FinančníZdraví - Finanční poradce

Webová aplikace pro sledování a zlepšování finančního zdraví jednotlivců. Aplikace je inspirována designem České spořitelny.

## Funkce

- Sledování příjmů a výdajů
- Kategorizace transakcí
- Rozpočtování
- Spořící cíle
- Personalizovaná finanční doporučení
- Skóre finančního zdraví
- **Revolut API integrace** - automatické načítání transakcí z Revolut účtu
  - **Business API** - pro firemní účty s API klíči
  - **Open Banking API** - pro osobní účty s OAuth autentifikací

## Technologie

- Backend: Flask (Python)
- Frontend: HTML, CSS, JavaScript, Bootstrap
- Databáze: SQLite
- Grafy: Chart.js

## Instalace a spuštění lokálně

1. Naklonujte repozitář
2. Vytvořte virtuální prostředí: `python -m venv venv`
3. Aktivujte virtuální prostředí:
   - Windows: `venv\Scripts\activate`
   - Mac/Linux: `source venv/bin/activate`
4. Nainstalujte závislosti: `pip install -r requirements.txt`
5. Spusťte aplikaci: `python app.py`
6. Otevřete prohlížeč na adrese: `http://localhost:5000`

## Nasazení na PythonAnywhere

1. Vytvořte účet na [PythonAnywhere](https://www.pythonanywhere.com/)
2. Nahrajte soubory projektu
3. Vytvořte novou webovou aplikaci
4. Nastavte Flask aplikaci s WSGI konfigurací ukazující na `wsgi.py`
5. Nainstalujte závislosti pomocí `pip install -r requirements.txt`
6. Restartujte webovou aplikaci

## Revolut API integrace

Aplikace podporuje dva způsoby integrace s Revolut:

### 1. Revolut Business API (pro firemní účty)

1. **Vytvořte Revolut Business účet** na [business.revolut.com](https://business.revolut.com)
2. **Přejděte do Nastavení > API** a vytvořte nový API certifikát
3. **Zkopírujte Client ID a Private Key**
4. **Nastavte environment variables:**
   ```bash
   export REVOLUT_CLIENT_ID="your_client_id"
   export REVOLUT_PRIVATE_KEY="your_private_key"
   export REVOLUT_SANDBOX="true"  # pro testování
   ```
5. **V aplikaci přejděte do Revolut > Business API** a nastavte ID vašeho účtu
6. **Klikněte na "Synchronizovat transakce"** pro načtení dat

### 2. Revolut Open Banking API (pro osobní účty)

1. **Zaregistrujte aplikaci** v [Revolut Developer Portal](https://developer.revolut.com/portal/signup)
2. **Získejte certifikáty** pro transport a signing
3. **Nastavte environment variables:**
   ```bash
   export REVOLUT_OB_CLIENT_ID="your_ob_client_id"
   export REVOLUT_OB_TRANSPORT_CERT="/path/to/transport.pem"
   export REVOLUT_OB_TRANSPORT_KEY="/path/to/transport.key"
   export REVOLUT_OB_SIGNING_KEY="/path/to/signing.key"
   export REVOLUT_OB_REDIRECT_URI="http://localhost:5000/revolut-retail/callback"
   export REVOLUT_OB_SANDBOX="true"  # pro testování
   ```
4. **V aplikaci přejděte do Revolut > Open Banking**
5. **Klikněte na "Připojit Revolut účet"** a dokončete OAuth autorizaci
6. **Transakce se automaticky synchronizují**

### Podporované funkce

- Automatické načítání transakcí z posledních 30-60 dní
- Mapování kategorií podle Merchant Category Codes (MCC)
- Detekce duplikátů transakcí
- Synchronizace stavu transakcí (pending, completed, atd.)
- Podpora více účtů (CZK, EUR, atd.)
- Bezpečná OAuth autentifikace (Open Banking)
- Standardní bankovní protokoly

## Demo uživatel

Pro testování aplikace můžete použít demo účet:
- Email: <EMAIL>
- Heslo: demo123

## Licence

Tento projekt je licencován pod MIT licencí.
