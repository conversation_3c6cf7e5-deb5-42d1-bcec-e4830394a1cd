# FinančníZdraví - Finanční poradce

Webová aplikace pro sledování a zlepšování finančního zdraví jednotlivců. Aplikace je inspirována designem České spořitelny.

## Funkce

- Sledování příjmů a výdajů
- Kategorizace transakcí
- Rozpočtování
- Spořící cíle
- Personalizovaná finanční doporučení
- Skóre finančního zdraví

## Technologie

- Backend: Flask (Python)
- Frontend: HTML, CSS, JavaScript, Bootstrap
- Databáze: SQLite
- Grafy: Chart.js

## Instalace a spuštění lokálně

1. Naklonujte repozitář
2. Vytvořte virtuální prostředí: `python -m venv venv`
3. Aktivujte virtuální prostředí:
   - Windows: `venv\Scripts\activate`
   - Mac/Linux: `source venv/bin/activate`
4. Nainstalujte závislosti: `pip install -r requirements.txt`
5. Spusťte aplikaci: `python app.py`
6. Otevřete prohlížeč na adrese: `http://localhost:5000`

## Nasazení na PythonAnywhere

1. Vytvořte účet na [PythonAnywhere](https://www.pythonanywhere.com/)
2. Nahrajte soubory projektu
3. Vytvořte novou webovou aplikaci
4. Nastavte Flask aplikaci s WSGI konfigurací ukazující na `wsgi.py`
5. Nainstalujte závislosti pomocí `pip install -r requirements.txt`
6. Restartujte webovou aplikaci

## Demo uživatel

Pro testování aplikace můžete použít demo účet:
- Email: <EMAIL>
- Heslo: demo123

## Licence

Tento projekt je licencován pod MIT licencí.
