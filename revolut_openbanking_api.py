"""
Revolut Open Banking API integrace pro osobní <PERSON> (retail)
"""
import os
import jwt
import time
import requests
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import json
import uuid
import base64
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.backends import default_backend


class RevolutOpenBankingAPI:
    """
    Třída pro práci s Revolut Open Banking API (retail/osobní ú<PERSON>ty)
    """
    
    def __init__(self):
        self.base_url = "https://oba-auth.revolut.com"
        self.sandbox_url = "https://oba-auth.revolut.com"  # Stejná URL pro sandbox
        self.is_sandbox = os.getenv('REVOLUT_OB_SANDBOX', 'true').lower() == 'true'
        
        # API credentials z environment variables
        self.client_id = os.getenv('REVOLUT_OB_CLIENT_ID')
        self.transport_cert_path = os.getenv('REVOLUT_OB_TRANSPORT_CERT')
        self.transport_key_path = os.getenv('REVOLUT_OB_TRANSPORT_KEY')
        self.signing_key_path = os.getenv('REVOLUT_OB_SIGNING_KEY')
        self.redirect_uri = os.getenv('REVOLUT_OB_REDIRECT_URI', 'http://localhost:5000/revolut/callback')
        
        # Tokeny
        self.access_token = None
        self.refresh_token = None
        self.token_expires_at = None
        self.refresh_token_expires_at = None
        
        # Demo režim pro testování bez skutečného API
        self.demo_mode = (self.client_id == 'demo_client_id' or 
                         not self.client_id or 
                         not self.transport_cert_path)
    
    def get_base_url(self) -> str:
        """Vrátí správnou base URL podle prostředí"""
        return self.sandbox_url if self.is_sandbox else self.base_url
    
    def get_client_credentials_token(self) -> str:
        """
        Získá client credentials token pro vytvoření consent
        """
        if self.demo_mode:
            return "demo_client_credentials_token"
        
        url = f"{self.get_base_url()}/token"
        
        data = {
            'grant_type': 'client_credentials',
            'scope': 'accounts',
            'client_id': self.client_id
        }
        
        response = requests.post(
            url,
            data=data,
            cert=(self.transport_cert_path, self.transport_key_path),
            headers={'Content-Type': 'application/x-www-form-urlencoded'}
        )
        
        if response.status_code == 200:
            token_data = response.json()
            return token_data['access_token']
        else:
            raise Exception(f"Chyba při získávání client credentials tokenu: {response.status_code} - {response.text}")
    
    def create_account_access_consent(self, permissions: List[str] = None) -> Dict:
        """
        Vytvoří consent pro přístup k účtům
        
        Args:
            permissions: Seznam oprávnění (výchozí: ReadAccountsBasic, ReadAccountsDetail)
        
        Returns:
            Consent data včetně ConsentId
        """
        if self.demo_mode:
            return self._get_demo_consent()
        
        if not permissions:
            permissions = ['ReadAccountsBasic', 'ReadAccountsDetail', 'ReadTransactionsBasic', 'ReadTransactionsDetail']
        
        client_token = self.get_client_credentials_token()
        
        url = f"{self.get_base_url()}/account-access-consents"
        
        # Consent data podle Open Banking standardu
        consent_data = {
            "Data": {
                "Permissions": permissions,
                "ExpirationDateTime": (datetime.now() + timedelta(days=90)).isoformat() + "+00:00",
                "TransactionFromDateTime": (datetime.now() - timedelta(days=90)).isoformat() + "+00:00",
                "TransactionToDateTime": datetime.now().isoformat() + "+00:00"
            },
            "Risk": {}
        }
        
        headers = {
            'Authorization': f'Bearer {client_token}',
            'Content-Type': 'application/json',
            'x-fapi-financial-id': '001580000103UAvAAM'
        }
        
        response = requests.post(url, json=consent_data, headers=headers,
                               cert=(self.transport_cert_path, self.transport_key_path))
        
        if response.status_code == 201:
            return response.json()
        else:
            raise Exception(f"Chyba při vytváření consent: {response.status_code} - {response.text}")
    
    def _get_demo_consent(self) -> Dict:
        """Vrátí demo consent pro testování"""
        return {
            "Data": {
                "Status": "AwaitingAuthorisation",
                "ConsentId": "demo-consent-id-12345",
                "Permissions": ["ReadAccountsBasic", "ReadAccountsDetail", "ReadTransactionsBasic", "ReadTransactionsDetail"],
                "ExpirationDateTime": (datetime.now() + timedelta(days=90)).isoformat() + "+00:00"
            }
        }
    
    def create_authorization_jwt(self, consent_id: str, state: str = None) -> str:
        """
        Vytvoří JWT pro autorizační URL
        
        Args:
            consent_id: ID consent z create_account_access_consent
            state: OAuth state parameter
        
        Returns:
            JWT token pro autorizační URL
        """
        if self.demo_mode:
            return "demo_jwt_token"
        
        if not state:
            state = str(uuid.uuid4())
        
        # JWT header
        header = {
            "alg": "PS256",
            "kid": self._get_signing_key_id()
        }
        
        # JWT payload
        now = int(time.time())
        payload = {
            "response_type": "code id_token",
            "client_id": self.client_id,
            "redirect_uri": self.redirect_uri,
            "aud": self.get_base_url(),
            "scope": "accounts",
            "state": state,
            "nbf": now,
            "exp": now + 3600,  # 1 hodina
            "claims": {
                "id_token": {
                    "openbanking_intent_id": {
                        "value": consent_id
                    }
                }
            }
        }
        
        # Načtení signing key
        with open(self.signing_key_path, 'rb') as f:
            signing_key = serialization.load_pem_private_key(
                f.read(),
                password=None,
                backend=default_backend()
            )
        
        # Vytvoření JWT
        token = jwt.encode(payload, signing_key, algorithm='PS256', headers=header)
        return token
    
    def _get_signing_key_id(self) -> str:
        """Získá key ID pro signing key"""
        # Pro demo účely vrátíme statické ID
        # V produkci by se mělo načítat z certifikátu
        return "demo-signing-key-id"
    
    def get_authorization_url(self, consent_id: str, state: str = None) -> str:
        """
        Vytvoří autorizační URL pro uživatele
        
        Args:
            consent_id: ID consent
            state: OAuth state parameter
        
        Returns:
            URL pro přesměrování uživatele
        """
        if self.demo_mode:
            return f"https://demo.revolut.com/authorize?consent_id={consent_id}&state={state or 'demo'}"
        
        jwt_token = self.create_authorization_jwt(consent_id, state)
        
        params = {
            'response_type': 'code id_token',
            'scope': 'accounts',
            'redirect_uri': self.redirect_uri,
            'client_id': self.client_id,
            'request': jwt_token
        }
        
        # URL encode parametrů
        param_string = '&'.join([f"{k}={requests.utils.quote(str(v))}" for k, v in params.items()])
        
        base_auth_url = "https://oba.revolut.com/ui/index.html" if not self.is_sandbox else "https://oba.revolut.com/ui/index.html"
        
        return f"{base_auth_url}?{param_string}"
    
    def exchange_code_for_token(self, authorization_code: str) -> Dict:
        """
        Vymění autorizační kód za access token
        
        Args:
            authorization_code: Kód z callback URL
        
        Returns:
            Token data včetně access_token a refresh_token
        """
        if self.demo_mode:
            return self._get_demo_tokens()
        
        url = f"{self.get_base_url()}/token"
        
        data = {
            'grant_type': 'authorization_code',
            'client_id': self.client_id,
            'code': authorization_code
        }
        
        response = requests.post(
            url,
            data=data,
            cert=(self.transport_cert_path, self.transport_key_path),
            headers={'Content-Type': 'application/x-www-form-urlencoded'}
        )
        
        if response.status_code == 200:
            token_data = response.json()
            
            # Uložíme tokeny
            self.access_token = token_data['access_token']
            self.refresh_token = token_data.get('refresh_token')
            
            # Nastavíme expiraci
            expires_in = token_data.get('expires_in', 3600)
            self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)
            
            if 'refresh_token_expires_at' in token_data:
                self.refresh_token_expires_at = datetime.fromtimestamp(token_data['refresh_token_expires_at'])
            
            return token_data
        else:
            raise Exception(f"Chyba při výměně kódu za token: {response.status_code} - {response.text}")
    
    def _get_demo_tokens(self) -> Dict:
        """Vrátí demo tokeny pro testování"""
        self.access_token = "demo_access_token"
        self.refresh_token = "demo_refresh_token"
        self.token_expires_at = datetime.now() + timedelta(hours=1)
        self.refresh_token_expires_at = datetime.now() + timedelta(days=180)
        
        return {
            "access_token": self.access_token,
            "refresh_token": self.refresh_token,
            "token_type": "Bearer",
            "expires_in": 3600
        }

    def refresh_access_token(self) -> str:
        """
        Obnoví access token pomocí refresh tokenu

        Returns:
            Nový access token
        """
        if self.demo_mode:
            return "demo_refreshed_access_token"

        if not self.refresh_token:
            raise ValueError("Refresh token není k dispozici")

        url = f"{self.get_base_url()}/token"

        data = {
            'grant_type': 'refresh_token',
            'client_id': self.client_id,
            'refresh_token': self.refresh_token
        }

        response = requests.post(
            url,
            data=data,
            cert=(self.transport_cert_path, self.transport_key_path),
            headers={'Content-Type': 'application/x-www-form-urlencoded'}
        )

        if response.status_code == 200:
            token_data = response.json()
            self.access_token = token_data['access_token']

            # Nastavíme novou expiraci
            expires_in = token_data.get('expires_in', 3600)
            self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)

            return self.access_token
        else:
            raise Exception(f"Chyba při obnovování tokenu: {response.status_code} - {response.text}")

    def get_valid_access_token(self) -> str:
        """
        Získá platný access token (obnoví pokud je potřeba)

        Returns:
            Platný access token
        """
        if self.demo_mode:
            return "demo_access_token"

        # Pokud nemáme token nebo expiroval
        if (not self.access_token or
            not self.token_expires_at or
            datetime.now() >= self.token_expires_at - timedelta(minutes=5)):

            if self.refresh_token:
                return self.refresh_access_token()
            else:
                raise ValueError("Access token expiroval a refresh token není k dispozici")

        return self.access_token

    def get_accounts(self) -> List[Dict]:
        """
        Získá seznam účtů uživatele

        Returns:
            Seznam účtů
        """
        if self.demo_mode:
            return self._get_demo_accounts()

        access_token = self.get_valid_access_token()

        url = f"{self.get_base_url()}/accounts"
        headers = {
            'Authorization': f'Bearer {access_token}',
            'x-fapi-financial-id': '001580000103UAvAAM'
        }

        response = requests.get(
            url,
            headers=headers,
            cert=(self.transport_cert_path, self.transport_key_path)
        )

        if response.status_code == 200:
            data = response.json()
            return data.get('Data', {}).get('Account', [])
        else:
            raise Exception(f"Chyba při získávání účtů: {response.status_code} - {response.text}")

    def _get_demo_accounts(self) -> List[Dict]:
        """Vrátí demo účty pro testování"""
        return [
            {
                "AccountId": "demo-account-czk-001",
                "Currency": "CZK",
                "AccountType": "Personal",
                "AccountSubType": "CurrentAccount",
                "Nickname": "Hlavní účet",
                "Account": [{
                    "SchemeName": "CZ.OBIE.AccountNumber",
                    "Identification": "*********/0100",
                    "Name": "Demo Uživatel"
                }]
            },
            {
                "AccountId": "demo-account-eur-002",
                "Currency": "EUR",
                "AccountType": "Personal",
                "AccountSubType": "CurrentAccount",
                "Nickname": "EUR účet",
                "Account": [{
                    "SchemeName": "CZ.OBIE.AccountNumber",
                    "Identification": "*********/0100",
                    "Name": "Demo Uživatel"
                }]
            }
        ]

    def get_account_transactions(self, account_id: str,
                               from_date: Optional[datetime] = None,
                               to_date: Optional[datetime] = None) -> List[Dict]:
        """
        Získá transakce pro konkrétní účet

        Args:
            account_id: ID účtu
            from_date: Datum od (volitelné)
            to_date: Datum do (volitelné)

        Returns:
            Seznam transakcí
        """
        if self.demo_mode:
            return self._get_demo_transactions(account_id, from_date, to_date)

        access_token = self.get_valid_access_token()

        url = f"{self.get_base_url()}/accounts/{account_id}/transactions"
        headers = {
            'Authorization': f'Bearer {access_token}',
            'x-fapi-financial-id': '001580000103UAvAAM'
        }

        # Přidáme parametry pro filtrování podle data
        params = {}
        if from_date:
            params['fromBookingDateTime'] = from_date.isoformat() + 'Z'
        if to_date:
            params['toBookingDateTime'] = to_date.isoformat() + 'Z'

        response = requests.get(
            url,
            headers=headers,
            params=params,
            cert=(self.transport_cert_path, self.transport_key_path)
        )

        if response.status_code == 200:
            data = response.json()
            return data.get('Data', {}).get('Transaction', [])
        else:
            raise Exception(f"Chyba při získávání transakcí: {response.status_code} - {response.text}")

    def _get_demo_transactions(self, account_id: str,
                              from_date: Optional[datetime] = None,
                              to_date: Optional[datetime] = None) -> List[Dict]:
        """Vrátí demo transakce pro testování"""
        import random

        if not from_date:
            from_date = datetime.now() - timedelta(days=30)
        if not to_date:
            to_date = datetime.now()

        # Demo transakce
        demo_transactions = []

        merchants = [
            "Tesco", "McDonald's", "Shell", "H&M", "Alza.cz",
            "Lékárna", "Kino", "Uber", "Spotify", "Netflix"
        ]

        for i in range(15):  # 15 demo transakcí
            # Náhodné datum v rozsahu
            days_diff = (to_date - from_date).days
            random_days = random.randint(0, max(1, days_diff))
            transaction_date = from_date + timedelta(days=random_days)

            # Náhodná částka
            if random.random() < 0.8:  # 80% výdajů
                amount = -random.randint(50, 2000)
                credit_debit = "Debit"
            else:  # 20% příjmů
                amount = random.randint(1000, 5000)
                credit_debit = "Credit"

            merchant = random.choice(merchants)

            transaction = {
                "TransactionId": f"demo-tx-{account_id}-{i+1:03d}",
                "BookingDateTime": transaction_date.isoformat() + "Z",
                "ValueDateTime": transaction_date.isoformat() + "Z",
                "Amount": {
                    "Amount": str(abs(amount)),
                    "Currency": "CZK" if "czk" in account_id else "EUR"
                },
                "CreditDebitIndicator": credit_debit,
                "Status": "Booked",
                "TransactionInformation": merchant,
                "MerchantDetails": {
                    "MerchantName": merchant
                } if credit_debit == "Debit" else None
            }

            demo_transactions.append(transaction)

        return demo_transactions

    def get_account_balance(self, account_id: str) -> Dict:
        """
        Získá zůstatek účtu

        Args:
            account_id: ID účtu

        Returns:
            Informace o zůstatku
        """
        if self.demo_mode:
            return self._get_demo_balance(account_id)

        access_token = self.get_valid_access_token()

        url = f"{self.get_base_url()}/accounts/{account_id}/balances"
        headers = {
            'Authorization': f'Bearer {access_token}',
            'x-fapi-financial-id': '001580000103UAvAAM'
        }

        response = requests.get(
            url,
            headers=headers,
            cert=(self.transport_cert_path, self.transport_key_path)
        )

        if response.status_code == 200:
            data = response.json()
            balances = data.get('Data', {}).get('Balance', [])
            # Vrátíme první dostupný zůstatek
            return balances[0] if balances else {}
        else:
            raise Exception(f"Chyba při získávání zůstatku: {response.status_code} - {response.text}")

    def _get_demo_balance(self, account_id: str) -> Dict:
        """Vrátí demo zůstatek pro testování"""
        import random

        currency = "CZK" if "czk" in account_id else "EUR"
        amount = random.randint(5000, 50000) if currency == "CZK" else random.randint(200, 2000)

        return {
            "Amount": {
                "Amount": str(amount),
                "Currency": currency
            },
            "CreditDebitIndicator": "Credit",
            "Type": "ClosingAvailable",
            "DateTime": datetime.now().isoformat() + "Z"
        }

    def test_connection(self) -> bool:
        """
        Otestuje připojení k Revolut Open Banking API
        """
        try:
            if self.demo_mode:
                print("Demo režim: Test připojení úspěšný")
                return True

            # Zkusíme získat client credentials token
            token = self.get_client_credentials_token()
            return bool(token)
        except Exception as e:
            print(f"Test připojení selhal: {e}")
            return False
