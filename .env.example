# Revolut Business API Configuration
# Získejte tyto hodnoty z Revolut Business > Nastavení > API

# Client ID z Revolut Business API
REVOLUT_CLIENT_ID=your_client_id_here

# Private key pro JWT podpis (m<PERSON><PERSON><PERSON> b<PERSON>t cesta k souboru nebo p<PERSON><PERSON><PERSON>)
REVOLUT_PRIVATE_KEY=your_private_key_here

# Prostředí - true pro sandbox, false pro produkci
REVOLUT_SANDBOX=true

# Revolut Open Banking API Configuration (pro osobní účty)
# Získejte tyto hodnoty z Revolut Developer Portal

# Client ID z Revolut Open Banking
REVOLUT_OB_CLIENT_ID=your_ob_client_id_here

# Cesty k certifikátům pro Open Banking
REVOLUT_OB_TRANSPORT_CERT=/path/to/transport.pem
REVOLUT_OB_TRANSPORT_KEY=/path/to/transport.key
REVOLUT_OB_SIGNING_KEY=/path/to/signing.key

# Redirect URI pro OAuth callback
REVOLUT_OB_REDIRECT_URI=http://localhost:5000/revolut-retail/callback

# Prostředí - true pro sandbox, false pro produkci
REVOLUT_OB_SANDBOX=true

# Flask aplikace
SECRET_KEY=your_secret_key_here
DATABASE_URL=sqlite:///finhealth.db

# Další nastavení
DEBUG=true
