// Charts for FinHealth App

// Common chart colors
const chartColors = {
    primary: '#4e73df',
    success: '#1cc88a',
    info: '#36b9cc',
    warning: '#f6c23e',
    danger: '#e74a3b',
    purple: '#6f42c1',
    orange: '#fd7e14',
    teal: '#20c9a6',
    gray: '#5a5c69',
    grayLight: '#858796'
};

// Common chart hover colors
const chartHoverColors = {
    primary: '#2e59d9',
    success: '#17a673',
    info: '#2c9faf',
    warning: '#dda20a',
    danger: '#be2617',
    purple: '#5a30a3',
    orange: '#d56308',
    teal: '#169b80',
    gray: '#3a3b45',
    grayLight: '#60616f'
};

// Create spending by category chart
function createSpendingChart(elementId, categories, amounts) {
    const ctx = document.getElementById(elementId).getContext('2d');
    
    // Generate colors array based on number of categories
    const backgroundColors = Object.values(chartColors).slice(0, categories.length);
    const hoverBackgroundColors = Object.values(chartHoverColors).slice(0, categories.length);
    
    const chart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: categories,
            datasets: [{
                data: amounts,
                backgroundColor: backgroundColors,
                hoverBackgroundColor: hoverBackgroundColors,
                hoverBorderColor: "rgba(234, 236, 244, 1)",
            }]
        },
        options: {
            maintainAspectRatio: false,
            tooltips: {
                callbacks: {
                    label: function(tooltipItem, data) {
                        const dataset = data.datasets[tooltipItem.datasetIndex];
                        const currentValue = dataset.data[tooltipItem.index];
                        return `${data.labels[tooltipItem.index]}: $${currentValue.toFixed(2)}`;
                    }
                }
            },
            legend: {
                position: 'right',
                labels: {
                    boxWidth: 12
                }
            },
            cutoutPercentage: 70,
        },
    });
    
    return chart;
}

// Create income vs expenses chart
function createIncomeExpensesChart(elementId, income, expenses) {
    const ctx = document.getElementById(elementId).getContext('2d');
    
    const chart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['Income', 'Expenses'],
            datasets: [{
                data: [income, expenses],
                backgroundColor: [chartColors.success, chartColors.danger],
                hoverBackgroundColor: [chartHoverColors.success, chartHoverColors.danger],
                hoverBorderColor: "rgba(234, 236, 244, 1)",
            }]
        },
        options: {
            maintainAspectRatio: false,
            scales: {
                yAxes: [{
                    ticks: {
                        beginAtZero: true,
                        callback: function(value) {
                            return '$' + value;
                        }
                    }
                }]
            },
            tooltips: {
                callbacks: {
                    label: function(tooltipItem, data) {
                        return '$' + tooltipItem.yLabel.toFixed(2);
                    }
                }
            },
            legend: {
                display: false
            }
        }
    });
    
    return chart;
}

// Create budget vs actual chart
function createBudgetVsActualChart(elementId, categories, budgetAmounts, actualAmounts) {
    const ctx = document.getElementById(elementId).getContext('2d');
    
    const chart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: categories,
            datasets: [
                {
                    label: 'Budget',
                    backgroundColor: 'rgba(78, 115, 223, 0.5)',
                    borderColor: 'rgba(78, 115, 223, 1)',
                    borderWidth: 1,
                    data: budgetAmounts
                },
                {
                    label: 'Actual',
                    backgroundColor: 'rgba(28, 200, 138, 0.5)',
                    borderColor: 'rgba(28, 200, 138, 1)',
                    borderWidth: 1,
                    data: actualAmounts
                }
            ]
        },
        options: {
            maintainAspectRatio: false,
            scales: {
                yAxes: [{
                    ticks: {
                        beginAtZero: true,
                        callback: function(value) {
                            return '$' + value;
                        }
                    }
                }]
            },
            tooltips: {
                callbacks: {
                    label: function(tooltipItem, data) {
                        return data.datasets[tooltipItem.datasetIndex].label + ': $' + tooltipItem.yLabel.toFixed(2);
                    }
                }
            }
        }
    });
    
    return chart;
}

// Create goals progress chart
function createGoalsChart(elementId, goalNames, currentAmounts, targetAmounts) {
    const ctx = document.getElementById(elementId).getContext('2d');
    
    const chart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: goalNames,
            datasets: [
                {
                    label: 'Current Amount',
                    backgroundColor: 'rgba(54, 185, 204, 0.5)',
                    borderColor: 'rgba(54, 185, 204, 1)',
                    borderWidth: 1,
                    data: currentAmounts
                },
                {
                    label: 'Target Amount',
                    backgroundColor: 'rgba(78, 115, 223, 0.5)',
                    borderColor: 'rgba(78, 115, 223, 1)',
                    borderWidth: 1,
                    data: targetAmounts
                }
            ]
        },
        options: {
            maintainAspectRatio: false,
            scales: {
                yAxes: [{
                    ticks: {
                        beginAtZero: true,
                        callback: function(value) {
                            return '$' + value;
                        }
                    }
                }]
            },
            tooltips: {
                callbacks: {
                    label: function(tooltipItem, data) {
                        return data.datasets[tooltipItem.datasetIndex].label + ': $' + tooltipItem.yLabel.toFixed(2);
                    }
                }
            }
        }
    });
    
    return chart;
}

// Create health score history chart
function createHealthScoreChart(elementId, labels, scores) {
    const ctx = document.getElementById(elementId).getContext('2d');
    
    const chart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Financial Health Score',
                data: scores,
                backgroundColor: 'rgba(78, 115, 223, 0.05)',
                borderColor: 'rgba(78, 115, 223, 1)',
                pointBackgroundColor: 'rgba(78, 115, 223, 1)',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: 'rgba(78, 115, 223, 1)',
                borderWidth: 2,
                fill: true
            }]
        },
        options: {
            maintainAspectRatio: false,
            scales: {
                yAxes: [{
                    ticks: {
                        min: 0,
                        max: 100,
                        stepSize: 20
                    }
                }]
            },
            tooltips: {
                callbacks: {
                    label: function(tooltipItem, data) {
                        return 'Score: ' + tooltipItem.yLabel;
                    }
                }
            }
        }
    });
    
    return chart;
}
