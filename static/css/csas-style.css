/* <PERSON><PERSON>y inspirované designem <PERSON><PERSON> spořitelny */

:root {
    /* <PERSON><PERSON><PERSON><PERSON> sch<PERSON> spořitelny */
    --csas-blue: #0d65d9;
    --csas-blue-light: #4a8aea;
    --csas-blue-dark: #0a4ca3;
    --csas-blue-bg: #f0f5ff;
    --csas-gray: #f5f5f5;
    --csas-gray-dark: #333333;
    --csas-gray-medium: #666666;
    --csas-gray-light: #e0e0e0;
    --csas-white: #ffffff;
    --csas-black: #000000;
    --csas-red: #e74c3c;
    --csas-green: #27ae60;
    --csas-yellow: #f39c12;
}

/* Základn<PERSON> styly */
body {
    background-color: var(--csas-gray);
    font-family: 'Open Sans', Arial, sans-serif;
    color: var(--csas-gray-dark);
}

/* Naviga<PERSON><PERSON><PERSON> l<PERSON> */
.top-navbar {
    background-color: var(--csas-blue);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1030;
    height: 60px;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--csas-white) !important;
    padding: 15px 20px;
}

.user-dropdown {
    color: var(--csas-white) !important;
}

/* Postranní panel */
.sidebar {
    position: fixed;
    top: 60px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);
    background-color: var(--csas-white);
    width: 240px;
    border-right: 1px solid var(--csas-gray-light);
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 60px);
    padding-top: 1rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: var(--csas-gray-dark);
    padding: 12px 20px;
    border-left: 3px solid transparent;
    transition: all 0.2s ease;
}

.sidebar .nav-link:hover {
    background-color: var(--csas-blue-bg);
    color: var(--csas-blue);
    border-left: 3px solid var(--csas-blue-light);
}

.sidebar .nav-link.active {
    background-color: var(--csas-blue-bg);
    color: var(--csas-blue);
    border-left: 3px solid var(--csas-blue);
}

.sidebar .nav-link i {
    margin-right: 10px;
    color: var(--csas-blue);
}

/* Hlavní obsah */
.main-content {
    margin-left: 240px;
    padding: 20px;
    margin-top: 60px;
}

/* Karty */
.card {
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    border: none;
    overflow: hidden;
}

.card-header {
    background-color: var(--csas-white);
    border-bottom: 1px solid var(--csas-gray-light);
    font-weight: 600;
    padding: 15px 20px;
}

.card-body {
    padding: 20px;
}

/* Tlačítka */
.btn-primary {
    background-color: var(--csas-blue);
    border-color: var(--csas-blue);
    border-radius: 30px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background-color: var(--csas-blue-dark);
    border-color: var(--csas-blue-dark);
}

.btn-secondary {
    background-color: var(--csas-gray);
    border-color: var(--csas-gray-light);
    color: var(--csas-gray-dark);
    border-radius: 30px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-secondary:hover {
    background-color: var(--csas-gray-light);
    border-color: var(--csas-gray-medium);
}

.btn-success {
    background-color: var(--csas-green);
    border-color: var(--csas-green);
    border-radius: 30px;
    padding: 8px 20px;
    font-weight: 500;
}

.btn-danger {
    background-color: var(--csas-red);
    border-color: var(--csas-red);
    border-radius: 30px;
    padding: 8px 20px;
    font-weight: 500;
}

.btn-info {
    background-color: var(--csas-blue-light);
    border-color: var(--csas-blue-light);
    border-radius: 30px;
    padding: 8px 20px;
    font-weight: 500;
}

/* Speciální karty */
.border-left-primary {
    border-left: 4px solid var(--csas-blue);
}

.border-left-success {
    border-left: 4px solid var(--csas-green);
}

.border-left-info {
    border-left: 4px solid var(--csas-blue-light);
}

.border-left-warning {
    border-left: 4px solid var(--csas-yellow);
}

.border-left-danger {
    border-left: 4px solid var(--csas-red);
}

/* Finanční zdraví */
.health-score {
    font-size: 3rem;
    font-weight: 700;
    color: var(--csas-blue);
}

/* Doporučení */
.recommendation {
    border-left: 4px solid;
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 0 8px 8px 0;
}

.recommendation.warning {
    border-left-color: var(--csas-yellow);
    background-color: #fff9e6;
}

.recommendation.info {
    border-left-color: var(--csas-blue-light);
    background-color: var(--csas-blue-bg);
}

.recommendation.success {
    border-left-color: var(--csas-green);
    background-color: #e6f9f1;
}

/* Progress bar */
.progress {
    height: 10px;
    margin-top: 5px;
    border-radius: 5px;
    background-color: var(--csas-gray-light);
}

.progress-bar {
    border-radius: 5px;
}

.bg-primary {
    background-color: var(--csas-blue) !important;
}

.bg-success {
    background-color: var(--csas-green) !important;
}

.bg-info {
    background-color: var(--csas-blue-light) !important;
}

.bg-warning {
    background-color: var(--csas-yellow) !important;
}

.bg-danger {
    background-color: var(--csas-red) !important;
}

/* Transakce */
.transaction-item {
    border-bottom: 1px solid var(--csas-gray-light);
    padding: 12px 0;
}

.transaction-item:last-child {
    border-bottom: none;
}

/* Text colors */
.text-primary {
    color: var(--csas-blue) !important;
}

.text-success {
    color: var(--csas-green) !important;
}

.text-info {
    color: var(--csas-blue-light) !important;
}

.text-warning {
    color: var(--csas-yellow) !important;
}

.text-danger {
    color: var(--csas-red) !important;
}

/* Formuláře */
.form-control {
    border-radius: 8px;
    border: 1px solid var(--csas-gray-light);
    padding: 10px 15px;
}

.form-control:focus {
    border-color: var(--csas-blue-light);
    box-shadow: 0 0 0 0.2rem rgba(13, 101, 217, 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--csas-gray-dark);
}

/* Alerty */
.alert {
    border-radius: 8px;
    border: none;
    padding: 15px;
}

.alert-info {
    background-color: var(--csas-blue-bg);
    color: var(--csas-blue-dark);
}

.alert-success {
    background-color: #e6f9f1;
    color: var(--csas-green);
}

.alert-warning {
    background-color: #fff9e6;
    color: var(--csas-yellow);
}

.alert-danger {
    background-color: #fde9e7;
    color: var(--csas-red);
}

/* Responzivní úpravy pro mobilní zařízení */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        position: static;
        height: auto;
        margin-top: 60px;
    }
    
    .main-content {
        margin-left: 0;
        margin-top: 0;
    }
    
    .sidebar-sticky {
        height: auto;
    }
    
    body {
        padding-top: 60px;
    }
}
