/* Main Styles for FinHealth App */

/* General Styles */
body {
    background-color: #f8f9fa;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    background-color: #4e73df;
    width: 240px;
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: .5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: white;
    padding: 10px 20px;
}

.sidebar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
}

.sidebar .nav-link i {
    margin-right: 10px;
}

/* Main Content Styles */
.main-content {
    margin-left: 240px;
    padding: 20px;
}

/* Card Styles */
.card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

.border-left-primary {
    border-left: 4px solid #4e73df;
}

.border-left-success {
    border-left: 4px solid #1cc88a;
}

.border-left-info {
    border-left: 4px solid #36b9cc;
}

.border-left-warning {
    border-left: 4px solid #f6c23e;
}

.border-left-danger {
    border-left: 4px solid #e74a3b;
}

/* Health Score Styles */
.health-score {
    font-size: 3rem;
    font-weight: 700;
}

/* Recommendation Styles */
.recommendation {
    border-left: 4px solid;
    padding: 10px 15px;
    margin-bottom: 10px;
}

.recommendation.warning {
    border-left-color: #f6c23e;
    background-color: #fff9e6;
}

.recommendation.info {
    border-left-color: #36b9cc;
    background-color: #e6f7fa;
}

.recommendation.success {
    border-left-color: #1cc88a;
    background-color: #e6f9f1;
}

/* Progress Bar Styles */
.progress {
    height: 10px;
    margin-top: 5px;
}

/* Transaction Styles */
.transaction-item {
    border-bottom: 1px solid #e3e6f0;
    padding: 10px 0;
}

.transaction-item:last-child {
    border-bottom: none;
}

/* Navbar Styles */
.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
    color: white !important;
    padding: 15px 20px;
}

.top-navbar {
    background-color: #4e73df;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-dropdown {
    color: white !important;
}

/* Text Colors */
.text-primary {
    color: #4e73df !important;
}

.text-success {
    color: #1cc88a !important;
}

.text-info {
    color: #36b9cc !important;
}

.text-warning {
    color: #f6c23e !important;
}

.text-danger {
    color: #e74a3b !important;
}

/* Background Colors */
.bg-primary {
    background-color: #4e73df !important;
}

.bg-success {
    background-color: #1cc88a !important;
}

.bg-info {
    background-color: #36b9cc !important;
}

.bg-warning {
    background-color: #f6c23e !important;
}

.bg-danger {
    background-color: #e74a3b !important;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        position: relative;
        padding-top: 0;
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .sidebar-sticky {
        height: auto;
    }
}
