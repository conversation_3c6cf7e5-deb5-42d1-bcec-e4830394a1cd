"""
Skript pro naplnění databáze testovacími daty.
Spusťte tento skript po vytvoření databáze, aby se vyt<PERSON><PERSON><PERSON> uk<PERSON>kov<PERSON> uživatel s daty.
"""

from app import app, db
from models import User, Transaction, Budget, SavingsGoal, Category, FinancialHealthScore
from datetime import datetime, timedelta
import random

# Kategorie výdajů a příjmů
EXPENSE_CATEGORIES = [
    'potraviny', 'restaurace', 'doprava', 'bydlení', 'energie', 
    'zábava', 'oblečení', 'zdraví', 'vzdělávání', 'dovolená', 
    'dárky', 'elektronika', 'sport', 'domácnost', 'ostatní'
]

INCOME_CATEGORIES = ['výplata', 'bonus', 'dárek', 'investice', 'vedlej<PERSON><PERSON> p<PERSON>', 'vratka daně']

# <PERSON><PERSON><PERSON>vy obchodů pro různé kategorie
STORE_NAMES = {
    'potraviny': ['<PERSON>', 'Lidl', '<PERSON><PERSON><PERSON>', 'Billa', 'Tesco', 'Penny Market', 'Globus'],
    'restaurace': ['Restaurace U Zlatého lva', 'Pizzerie Milano', 'Sushi Bar Tokyo', 'Kavárna Dobrá čajovna', 'Burger King', 'McDonald\'s', 'KFC'],
    'doprava': ['České dráhy', 'RegioJet', 'Dopravní podnik', 'Uber', 'Bolt', 'Shell', 'MOL', 'OMV'],
    'bydlení': ['Nájem', 'Hypotéka', 'Pojištění domácnosti'],
    'energie': ['ČEZ', 'E.ON', 'Innogy', 'Pražská plynárenská', 'Pražská energetika'],
    'zábava': ['Netflix', 'Spotify', 'Cinema City', 'HBO Max', 'Steam', 'PlayStation Store', 'Ticketportal'],
    'oblečení': ['H&M', 'Zara', 'C&A', 'Reserved', 'Peek & Cloppenburg', 'Sportisimo', 'Decathlon'],
    'zdraví': ['Dr. Max', 'Benu', 'Lékárna', 'Fitness centrum', 'Masáže', 'Optika'],
    'vzdělávání': ['Kurz angličtiny', 'Odborná literatura', 'Online kurz', 'Školné'],
    'dovolená': ['Invia', 'Booking.com', 'Airbnb', 'České aerolinie', 'Smartwings'],
    'dárky': ['Alza', 'CZC', 'Datart', 'Parfumerie', 'Knihkupectví'],
    'elektronika': ['Alza', 'CZC', 'Datart', 'Apple Store', 'Samsung', 'MediaMarkt'],
    'sport': ['Sportisimo', 'Decathlon', 'Hervis', 'A3 Sport', 'Permanentka fitness'],
    'domácnost': ['IKEA', 'JYSK', 'Möbelix', 'OBI', 'Hornbach', 'Bauhaus'],
    'ostatní': ['Různé výdaje', 'Drobné nákupy', 'Poplatky', 'Předplatné'],
    'výplata': ['Výplata', 'Mzda'],
    'bonus': ['Roční bonus', 'Kvartální bonus', 'Odměna'],
    'dárek': ['Dárek k narozeninám', 'Vánoční dárek', 'Finanční dar'],
    'investice': ['Dividendy', 'Úroky z investic', 'Výnos z podílových fondů'],
    'vedlejší příjem': ['Brigáda', 'Přivýdělek', 'Freelance'],
    'vratka daně': ['Daňový bonus', 'Vratka daně']
}

def create_demo_user():
    """Vytvoří ukázkového uživatele"""
    # Kontrola, zda uživatel již existuje
    if User.query.filter_by(email='<EMAIL>').first():
        print("Demo uživatel již existuje.")
        return User.query.filter_by(email='<EMAIL>').first()
    
    user = User(
        username='demo',
        email='<EMAIL>'
    )
    user.set_password('demo123')
    
    db.session.add(user)
    db.session.commit()
    print(f"Vytvořen demo uživatel (ID: {user.id})")
    return user

def create_categories():
    """Vytvoří kategorie transakcí"""
    # Kontrola, zda kategorie již existují
    if Category.query.count() > 0:
        print("Kategorie již existují.")
        return
    
    # Vytvoření kategorií výdajů
    for category in EXPENSE_CATEGORIES:
        db.session.add(Category(name=category, type='expense'))
    
    # Vytvoření kategorií příjmů
    for category in INCOME_CATEGORIES:
        db.session.add(Category(name=category, type='income'))
    
    db.session.commit()
    print(f"Vytvořeno {len(EXPENSE_CATEGORIES) + len(INCOME_CATEGORIES)} kategorií")

def create_transactions(user, num_months=6):
    """Vytvoří transakce pro uživatele za posledních několik měsíců"""
    # Kontrola, zda uživatel již má transakce
    if Transaction.query.filter_by(user_id=user.id).count() > 0:
        print(f"Uživatel {user.username} již má transakce.")
        return
    
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30 * num_months)
    current_date = start_date
    
    # Měsíční příjem
    monthly_income = 35000
    
    # Měsíční výdaje podle kategorií (průměrné hodnoty)
    monthly_expenses = {
        'potraviny': 6000,
        'restaurace': 2000,
        'doprava': 1500,
        'bydlení': 12000,
        'energie': 3000,
        'zábava': 1500,
        'oblečení': 2000,
        'zdraví': 1000,
        'vzdělávání': 500,
        'dovolená': 2000,
        'dárky': 500,
        'elektronika': 1000,
        'sport': 800,
        'domácnost': 1000,
        'ostatní': 1000
    }
    
    transactions = []
    
    # Pro každý měsíc
    while current_date < end_date:
        month_start = current_date.replace(day=1)
        if month_start.month == 12:
            month_end = month_start.replace(year=month_start.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            month_end = month_start.replace(month=month_start.month + 1, day=1) - timedelta(days=1)
        
        # Příjem - výplata (15. den v měsíci)
        salary_date = month_start.replace(day=15)
        if salary_date.weekday() >= 5:  # pokud je víkend, posunout na pátek
            salary_date = salary_date - timedelta(days=salary_date.weekday() - 4)
        
        transactions.append(
            Transaction(
                user_id=user.id,
                amount=monthly_income,
                currency='CZK',
                description=random.choice(STORE_NAMES['výplata']),
                category='výplata',
                transaction_date=salary_date,
                transaction_type='income'
            )
        )
        
        # Občasný bonus nebo vedlejší příjem
        if random.random() < 0.3:  # 30% šance na bonus každý měsíc
            bonus_amount = random.randint(2000, 5000)
            bonus_date = month_start + timedelta(days=random.randint(1, 28))
            bonus_category = random.choice(['bonus', 'vedlejší příjem'])
            
            transactions.append(
                Transaction(
                    user_id=user.id,
                    amount=bonus_amount,
                    currency='CZK',
                    description=random.choice(STORE_NAMES[bonus_category]),
                    category=bonus_category,
                    transaction_date=bonus_date,
                    transaction_type='income'
                )
            )
        
        # Výdaje
        for category, avg_amount in monthly_expenses.items():
            # Variabilita ve výdajích
            variation = random.uniform(0.8, 1.2)
            category_amount = avg_amount * variation
            
            # Počet transakcí v kategorii
            if category in ['bydlení', 'energie']:
                num_transactions = 1  # Jedna platba měsíčně
            elif category in ['potraviny', 'restaurace']:
                num_transactions = random.randint(5, 10)  # Více transakcí
            else:
                num_transactions = random.randint(1, 4)
            
            # Rozdělení částky na transakce
            amounts = []
            remaining = category_amount
            for i in range(num_transactions - 1):
                amount = remaining * random.uniform(0.1, 0.5)
                amounts.append(amount)
                remaining -= amount
            amounts.append(remaining)
            
            # Vytvoření transakcí
            for amount in amounts:
                transaction_date = month_start + timedelta(days=random.randint(1, (month_end - month_start).days))
                store_name = random.choice(STORE_NAMES.get(category, ['Neznámý obchod']))
                
                transactions.append(
                    Transaction(
                        user_id=user.id,
                        amount=-round(amount, 2),  # Záporná hodnota pro výdaje
                        currency='CZK',
                        description=store_name,
                        category=category,
                        transaction_date=transaction_date,
                        transaction_type='expense'
                    )
                )
        
        # Posun na další měsíc
        current_date = month_end + timedelta(days=1)
    
    # Uložení všech transakcí do databáze
    db.session.add_all(transactions)
    db.session.commit()
    print(f"Vytvořeno {len(transactions)} transakcí pro uživatele {user.username}")

def create_budgets(user):
    """Vytvoří rozpočty pro uživatele"""
    # Kontrola, zda uživatel již má rozpočty
    if Budget.query.filter_by(user_id=user.id).count() > 0:
        print(f"Uživatel {user.username} již má rozpočty.")
        return
    
    # Měsíční rozpočty podle kategorií
    monthly_budgets = {
        'potraviny': 7000,
        'restaurace': 2500,
        'doprava': 2000,
        'bydlení': 12000,
        'energie': 3500,
        'zábava': 2000,
        'oblečení': 2500,
        'zdraví': 1500,
        'vzdělávání': 1000,
        'dovolená': 3000,
        'elektronika': 1500,
        'sport': 1000,
        'domácnost': 1500,
        'ostatní': 1500
    }
    
    budgets = []
    
    for category, amount in monthly_budgets.items():
        budgets.append(
            Budget(
                user_id=user.id,
                category=category,
                amount=amount,
                period='monthly'
            )
        )
    
    db.session.add_all(budgets)
    db.session.commit()
    print(f"Vytvořeno {len(budgets)} rozpočtů pro uživatele {user.username}")

def create_savings_goals(user):
    """Vytvoří spořící cíle pro uživatele"""
    # Kontrola, zda uživatel již má spořící cíle
    if SavingsGoal.query.filter_by(user_id=user.id).count() > 0:
        print(f"Uživatel {user.username} již má spořící cíle.")
        return
    
    # Definice spořících cílů
    goals = [
        {
            'name': 'Nové auto',
            'target_amount': 300000,
            'current_amount': 120000,
            'target_date': datetime.now() + timedelta(days=365)
        },
        {
            'name': 'Dovolená',
            'target_amount': 50000,
            'current_amount': 30000,
            'target_date': datetime.now() + timedelta(days=180)
        },
        {
            'name': 'Nový notebook',
            'target_amount': 25000,
            'current_amount': 15000,
            'target_date': datetime.now() + timedelta(days=90)
        },
        {
            'name': 'Rezerva na nečekané výdaje',
            'target_amount': 100000,
            'current_amount': 60000,
            'target_date': None
        }
    ]
    
    savings_goals = []
    
    for goal in goals:
        savings_goals.append(
            SavingsGoal(
                user_id=user.id,
                name=goal['name'],
                target_amount=goal['target_amount'],
                current_amount=goal['current_amount'],
                target_date=goal['target_date']
            )
        )
    
    db.session.add_all(savings_goals)
    db.session.commit()
    print(f"Vytvořeno {len(savings_goals)} spořících cílů pro uživatele {user.username}")

def calculate_financial_health(user):
    """Vypočítá a uloží skóre finančního zdraví pro uživatele"""
    # Import zde, abychom předešli cyklickým importům
    from financial_analysis import calculate_financial_health_score
    
    # Kontrola, zda uživatel již má skóre finančního zdraví
    if FinancialHealthScore.query.filter_by(user_id=user.id).count() > 0:
        print(f"Uživatel {user.username} již má skóre finančního zdraví.")
        return
    
    # Výpočet skóre
    score = calculate_financial_health_score(user.id)
    print(f"Vypočítáno skóre finančního zdraví pro uživatele {user.username}: {score}")

def seed_database():
    """Hlavní funkce pro naplnění databáze testovacími daty"""
    with app.app_context():
        # Vytvoření tabulek, pokud neexistují
        db.create_all()
        
        # Vytvoření kategorií
        create_categories()
        
        # Vytvoření demo uživatele
        user = create_demo_user()
        
        # Vytvoření transakcí
        create_transactions(user)
        
        # Vytvoření rozpočtů
        create_budgets(user)
        
        # Vytvoření spořících cílů
        create_savings_goals(user)
        
        # Výpočet finančního zdraví
        calculate_financial_health(user)
        
        print("Databáze byla úspěšně naplněna testovacími daty.")

if __name__ == "__main__":
    seed_database()
