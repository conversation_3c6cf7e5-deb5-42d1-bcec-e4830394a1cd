from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime

db = SQLAlchemy()

class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Revolut Business API integrace
    revolut_account_id = db.Column(db.String(120), nullable=True)
    revolut_last_sync = db.Column(db.DateTime, nullable=True)
    revolut_enabled = db.Column(db.Boolean, default=False)

    # Revolut Open Banking integrace (retail)
    revolut_ob_consent_id = db.Column(db.String(120), nullable=True)
    revolut_ob_consent_status = db.Column(db.String(50), nullable=True)
    revolut_ob_access_token = db.Column(db.Text, nullable=True)
    revolut_ob_refresh_token = db.Column(db.Text, nullable=True)
    revolut_ob_token_expires_at = db.Column(db.DateTime, nullable=True)
    revolut_ob_last_sync = db.Column(db.DateTime, nullable=True)
    revolut_ob_enabled = db.Column(db.Boolean, default=False)

    transactions = db.relationship('Transaction', backref='user', lazy=True)
    budgets = db.relationship('Budget', backref='user', lazy=True)
    goals = db.relationship('SavingsGoal', backref='user', lazy=True)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
        
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Transaction(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    currency = db.Column(db.String(3), nullable=False, default='CZK')
    description = db.Column(db.String(200))
    category = db.Column(db.String(50))
    transaction_date = db.Column(db.DateTime, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    transaction_type = db.Column(db.String(20))  # income, expense, transfer

    # Podpora externí synchronizace (Revolut, atd.)
    external_id = db.Column(db.String(120), nullable=True)  # ID z externí služby
    source = db.Column(db.String(50), default='manual')  # manual, revolut, atd.
    completed_at = db.Column(db.DateTime, nullable=True)  # Datum dokončení transakce
    
class Category(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    type = db.Column(db.String(20))  # income, expense
    
class Budget(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    category = db.Column(db.String(50), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    period = db.Column(db.String(20), nullable=False)  # monthly, weekly, yearly
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
class SavingsGoal(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    target_amount = db.Column(db.Float, nullable=False)
    current_amount = db.Column(db.Float, default=0.0)
    target_date = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
class FinancialHealthScore(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    score = db.Column(db.Integer, nullable=False)  # 0-100
    savings_ratio = db.Column(db.Float)  # percentage of income saved
    debt_to_income = db.Column(db.Float)  # debt payments / income
    budget_adherence = db.Column(db.Float)  # how well user follows budget
    emergency_fund = db.Column(db.Float)  # months of expenses covered
    calculated_at = db.Column(db.DateTime, default=datetime.utcnow)
