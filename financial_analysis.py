from models import Transaction, Budget, FinancialHealthScore, db
from datetime import datetime, timedelta
from sqlalchemy import func
import numpy as np

def categorize_transaction(description, amount):
    """
    Automatically categorize a transaction based on its description and amount
    """
    description = description.lower()
    
    # Basic categorization rules
    categories = {
        'groceries': ['supermarket', 'grocery', 'food', 'market'],
        'dining': ['restaurant', 'cafe', 'coffee', 'bar', 'pub'],
        'transportation': ['uber', 'lyft', 'taxi', 'transport', 'train', 'bus', 'subway', 'metro'],
        'utilities': ['electric', 'water', 'gas', 'internet', 'phone', 'utility'],
        'housing': ['rent', 'mortgage', 'apartment', 'house'],
        'entertainment': ['movie', 'cinema', 'theater', 'concert', 'netflix', 'spotify', 'subscription'],
        'shopping': ['amazon', 'store', 'shop', 'retail', 'clothing', 'electronics'],
        'health': ['doctor', 'medical', 'pharmacy', 'healthcare', 'fitness', 'gym'],
        'education': ['school', 'university', 'college', 'course', 'class', 'book'],
        'income': ['salary', 'deposit', 'payment received', 'refund']
    }
    
    for category, keywords in categories.items():
        for keyword in keywords:
            if keyword in description:
                return category
    
    # Default category based on amount
    if amount > 0:
        return 'income'
    else:
        return 'other'

def calculate_monthly_spending_by_category(user_id):
    """
    Calculate monthly spending by category
    """
    # Get date range for the last month
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=30)
    
    # Query transactions grouped by category
    spending = db.session.query(
        Transaction.category,
        func.sum(Transaction.amount).label('total')
    ).filter(
        Transaction.user_id == user_id,
        Transaction.transaction_date.between(start_date, end_date),
        Transaction.amount < 0  # Only expenses (negative amounts)
    ).group_by(Transaction.category).all()
    
    return {category: abs(total) for category, total in spending}

def calculate_income_vs_expenses(user_id, months=1):
    """
    Calculate income vs expenses for a given period
    """
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=30 * months)
    
    # Calculate total income
    income = db.session.query(
        func.sum(Transaction.amount)
    ).filter(
        Transaction.user_id == user_id,
        Transaction.transaction_date.between(start_date, end_date),
        Transaction.amount > 0
    ).scalar() or 0
    
    # Calculate total expenses
    expenses = db.session.query(
        func.sum(Transaction.amount)
    ).filter(
        Transaction.user_id == user_id,
        Transaction.transaction_date.between(start_date, end_date),
        Transaction.amount < 0
    ).scalar() or 0
    
    return {
        'income': income,
        'expenses': abs(expenses),
        'net': income + expenses  # expenses are negative, so we add
    }

def calculate_budget_adherence(user_id):
    """
    Calculate how well the user is adhering to their budget
    """
    # Get all budget categories
    budgets = Budget.query.filter_by(user_id=user_id).all()
    
    if not budgets:
        return 0  # No budgets set
    
    adherence_scores = []
    
    for budget in budgets:
        # Get spending for this category in the current month
        spending = db.session.query(
            func.sum(Transaction.amount)
        ).filter(
            Transaction.user_id == user_id,
            Transaction.category == budget.category,
            Transaction.transaction_date >= datetime.utcnow().replace(day=1, hour=0, minute=0, second=0),
            Transaction.amount < 0
        ).scalar() or 0
        
        spending = abs(spending)
        
        # Calculate adherence as percentage of budget used
        if budget.amount > 0:
            adherence = min(spending / budget.amount, 1.0)
            adherence_scores.append(1.0 - adherence)  # Higher score for less spending
    
    # Average adherence across all categories
    if adherence_scores:
        return sum(adherence_scores) / len(adherence_scores)
    return 0

def calculate_savings_ratio(user_id, months=3):
    """
    Calculate savings ratio (savings / income)
    """
    financial_data = calculate_income_vs_expenses(user_id, months)
    
    if financial_data['income'] > 0:
        return max(0, financial_data['net'] / financial_data['income'])
    return 0

def calculate_emergency_fund_months(user_id):
    """
    Calculate how many months of expenses are covered by savings
    """
    # Get average monthly expenses
    monthly_expenses = calculate_income_vs_expenses(user_id, 3)['expenses'] / 3
    
    if monthly_expenses <= 0:
        return 0
    
    # Estimate savings from positive balance transactions
    savings = db.session.query(
        func.sum(Transaction.amount)
    ).filter(
        Transaction.user_id == user_id,
        Transaction.category.in_(['savings', 'investment']),
        Transaction.amount > 0
    ).scalar() or 0
    
    return savings / monthly_expenses if monthly_expenses > 0 else 0

def calculate_financial_health_score(user_id):
    """
    Calculate overall financial health score (0-100)
    """
    # Get component scores
    savings_ratio = calculate_savings_ratio(user_id)
    budget_adherence = calculate_budget_adherence(user_id)
    emergency_fund = calculate_emergency_fund_months(user_id)
    
    # Calculate income vs expenses
    financial_data = calculate_income_vs_expenses(user_id, 3)
    income_expense_ratio = 0
    if financial_data['expenses'] > 0:
        income_expense_ratio = min(financial_data['income'] / financial_data['expenses'], 2) / 2  # Scale 0-1
    
    # Weight the components
    weights = {
        'savings_ratio': 0.3,
        'budget_adherence': 0.2,
        'emergency_fund': 0.3,
        'income_expense_ratio': 0.2
    }
    
    # Calculate weighted score
    score = (
        savings_ratio * weights['savings_ratio'] * 100 +
        budget_adherence * weights['budget_adherence'] * 100 +
        min(emergency_fund / 6, 1) * weights['emergency_fund'] * 100 +  # 6 months emergency fund is ideal
        income_expense_ratio * weights['income_expense_ratio'] * 100
    )
    
    # Save the score
    health_score = FinancialHealthScore(
        user_id=user_id,
        score=int(score),
        savings_ratio=savings_ratio,
        budget_adherence=budget_adherence,
        emergency_fund=emergency_fund,
        calculated_at=datetime.utcnow()
    )
    
    db.session.add(health_score)
    db.session.commit()
    
    return int(score)
