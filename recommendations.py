from models import Transaction, Budget, FinancialHealthScore, db
from financial_analysis import (
    calculate_monthly_spending_by_category,
    calculate_income_vs_expenses,
    calculate_budget_adherence,
    calculate_savings_ratio,
    calculate_emergency_fund_months
)
from datetime import datetime, timedelta
from sqlalchemy import func

def get_spending_recommendations(user_id):
    """
    Generuje doporučení pro snížení výdajů
    """
    recommendations = []

    # Získání výdajů podle kategorií
    spending = calculate_monthly_spending_by_category(user_id)

    # Získání příjmů vs výdajů
    financial_data = calculate_income_vs_expenses(user_id)

    # Kontrola, zda výdaje převyšují příjmy
    if financial_data['expenses'] > financial_data['income']:
        recommendations.append({
            'type': 'warning',
            'title': 'Výdaje převyšují příjmy',
            'description': 'Vaše výdaje jsou vyšší než vaše příjmy. Zvažte sníž<PERSON><PERSON> výdajů nebo nalezení dodatečných zdrojů příjmů.'
        })

    # Kontrola kategorií s vysokými výdaji
    total_spending = sum(spending.values())
    for category, amount in spending.items():
        if amount > 0.3 * total_spending:  # Pokud kategorie tvoří více než 30 % celkových výdajů
            recommendations.append({
                'type': 'info',
                'title': f'Vysoké výdaje v kategorii {category}',
                'description': f'Utratili jste {amount:.2f} Kč za {category}, což je {(amount/total_spending)*100:.1f}% vašich celkových výdajů. Zvažte způsoby, jak tento výdaj snížit.'
            })

    # Kontrola dodržování rozpočtu
    budget_adherence = calculate_budget_adherence(user_id)
    if budget_adherence < 0.7:  # Méně než 70% dodržování
        recommendations.append({
            'type': 'warning',
            'title': 'Nízké dodržování rozpočtu',
            'description': 'V některých kategoriích utrácíte více, než máte v rozpočtu. Přezkoumejte svůj rozpočet a výdajové návyky.'
        })

    return recommendations

def get_savings_recommendations(user_id):
    """
    Generuje doporučení pro zlepšení úspor
    """
    recommendations = []

    # Výpočet poměru úspor
    savings_ratio = calculate_savings_ratio(user_id)

    # Kontrola poměru úspor
    if savings_ratio < 0.1:  # Méně než 10% úspor
        recommendations.append({
            'type': 'warning',
            'title': 'Nízká míra úspor',
            'description': 'Spoříte méně než 10 % svých příjmů. Snažte se spořit alespoň 15-20 % pro dlouhodobé finanční zdraví.'
        })
    elif savings_ratio < 0.2:  # Méně než 20% úspor
        recommendations.append({
            'type': 'info',
            'title': 'Střední míra úspor',
            'description': 'Spoříte mezi 10-20 % svých příjmů. To je dobré, ale zvažte zvýšení na 20 % pro lepší finanční zabezpečení.'
        })
    else:
        recommendations.append({
            'type': 'success',
            'title': 'Vynikající míra úspor',
            'description': 'Spoříte více než 20 % svých příjmů. Jen tak dál!'
        })

    # Kontrola rezervního fondu
    emergency_months = calculate_emergency_fund_months(user_id)
    if emergency_months < 3:
        recommendations.append({
            'type': 'warning',
            'title': 'Nízký rezervní fond',
            'description': f'Váš rezervní fond pokrývá přibližně {emergency_months:.1f} měsíců výdajů. Snažte se mít rezervu alespoň na 3-6 měsíců.'
        })
    elif emergency_months < 6:
        recommendations.append({
            'type': 'info',
            'title': 'Navyšte svůj rezervní fond',
            'description': f'Váš rezervní fond pokrývá přibližně {emergency_months:.1f} měsíců výdajů. Zvažte jeho navýšení na 6 měsíců pro lepší zabezpečení.'
        })

    return recommendations

def get_budget_recommendations(user_id):
    """
    Generuje doporučení pro zlepšení rozpočtu
    """
    recommendations = []

    # Získání všech rozpočtů
    budgets = Budget.query.filter_by(user_id=user_id).all()

    # Získání výdajů podle kategorií
    spending = calculate_monthly_spending_by_category(user_id)

    # Kontrola, zda uživatel má rozpočty
    if not budgets:
        recommendations.append({
            'type': 'info',
            'title': 'Vytvořte rozpočet',
            'description': 'Nemáte nastavené žádné rozpočty. Vytvoření rozpočtu je prvním krokem k finanční kontrole.'
        })
        return recommendations

    # Kontrola kategorií rozpočtu proti výdajům
    budget_categories = {budget.category for budget in budgets}
    spending_categories = set(spending.keys())

    # Nalezení kategorií s výdaji, ale bez rozpočtu
    unbudgeted = spending_categories - budget_categories
    if unbudgeted:
        for category in unbudgeted:
            recommendations.append({
                'type': 'info',
                'title': f'Vytvořte rozpočet pro kategorii {category}',
                'description': f'Utratili jste {spending[category]:.2f} Kč za {category}, ale nemáte pro tuto kategorii rozpočet.'
            })

    # Kontrola každého rozpočtu proti skutečným výdajům
    for budget in budgets:
        if budget.category in spending:
            actual = spending[budget.category]
            if actual > budget.amount:
                recommendations.append({
                    'type': 'warning',
                    'title': f'Překročení rozpočtu v kategorii {budget.category}',
                    'description': f'Utratili jste {actual:.2f} Kč za {budget.category}, což je o {actual - budget.amount:.2f} Kč více než váš rozpočet {budget.amount:.2f} Kč.'
                })
            elif actual < 0.5 * budget.amount:
                recommendations.append({
                    'type': 'info',
                    'title': f'Úprava rozpočtu pro kategorii {budget.category}',
                    'description': f'Utratili jste pouze {actual:.2f} Kč z vašeho rozpočtu {budget.amount:.2f} Kč pro {budget.category}. Zvažte úpravu rozpočtu nebo přerozdělení prostředků.'
                })

    return recommendations

def get_all_recommendations(user_id):
    """
    Získá všechna doporučení pro uživatele
    """
    recommendations = []

    # Získání doporučení z každé kategorie
    recommendations.extend(get_spending_recommendations(user_id))
    recommendations.extend(get_savings_recommendations(user_id))
    recommendations.extend(get_budget_recommendations(user_id))

    # Přidání obecných doporučení na základě skóre finančního zdraví
    latest_score = FinancialHealthScore.query.filter_by(user_id=user_id).order_by(FinancialHealthScore.calculated_at.desc()).first()

    if latest_score:
        if latest_score.score < 40:
            recommendations.append({
                'type': 'warning',
                'title': 'Zlepšete své finanční zdraví',
                'description': 'Vaše skóre finančního zdraví je nízké. Zaměřte se na snížení dluhů, zvýšení úspor a kontrolu výdajů.'
            })
        elif latest_score.score < 70:
            recommendations.append({
                'type': 'info',
                'title': 'Budujte finanční sílu',
                'description': 'Vaše finanční zdraví je průměrné. Pokračujte v budování rezervního fondu a splácení dluhů s vysokým úrokem.'
            })
        else:
            recommendations.append({
                'type': 'success',
                'title': 'Optimalizujte své finance',
                'description': 'Vaše finanční zdraví je dobré. Zvažte optimalizaci investic a plánování dlouhodobých cílů.'
            })

    return recommendations
