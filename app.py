from flask import Flask, render_template, jsonify, request, session, redirect, url_for, flash, abort
import requests
from datetime import datetime, timedelta
import os
from models import db, User, Transaction, Budget, SavingsGoal, FinancialHealthScore, Category
from auth import auth, login_required
from financial_analysis import (
    categorize_transaction,
    calculate_monthly_spending_by_category,
    calculate_income_vs_expenses,
    calculate_financial_health_score
)
from recommendations import get_all_recommendations
from revolut_sync import RevolutSyncService, setup_revolut_for_user

app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'your-secret-key')  # Use environment variable in production
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///finhealth.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Nastavení povolených hostitelů pro přístup k aplikaci
# Pro lokální vývoj povolíme všechny hostitele
app.config['ALLOWED_HOSTS'] = []  # Prázdný seznam znamená, že jsou povoleni všichni hostitelé

# Fix for Heroku PostgreSQL URI
if app.config['SQLALCHEMY_DATABASE_URI'] and app.config['SQLALCHEMY_DATABASE_URI'].startswith('postgres://'):
    app.config['SQLALCHEMY_DATABASE_URI'] = app.config['SQLALCHEMY_DATABASE_URI'].replace('postgres://', 'postgresql://', 1)

# Initialize the database
db.init_app(app)

# Middleware pro kontrolu ALLOWED_HOSTS - pro lokální vývoj je deaktivováno
@app.before_request
def check_allowed_hosts():
    # Získáme seznam povolených hostitelů
    allowed_hosts = app.config.get('ALLOWED_HOSTS', [])
    # Pokud je seznam prázdný, povolíme všechny hostitele
    if not allowed_hosts:
        return None
    # Jinak kontrolujeme, zda je aktuální hostitel v seznamu povolených
    host = request.host.split(':')[0]
    if host not in allowed_hosts:
        abort(403)  # Forbidden

# Register blueprints
app.register_blueprint(auth, url_prefix='/auth')

# Create a blueprint for main routes
from flask import Blueprint
main = Blueprint('main', __name__)

@main.route('/')
def index():
    if 'user_id' in session:
        return redirect(url_for('main.dashboard'))
    return render_template('index.html')

@main.route('/dashboard')
@login_required
def dashboard():
    user_id = session.get('user_id')

    # Získání finančních dat
    spending_by_category = calculate_monthly_spending_by_category(user_id)
    income_vs_expenses = calculate_income_vs_expenses(user_id)

    # Pokud uživatel nemá žádná data, vytvoříme mu testovací data
    total_transactions = Transaction.query.filter_by(user_id=user_id).count()
    if total_transactions == 0:
        from seed_data import create_transactions, create_budgets, create_savings_goals
        try:
            # Vytvoříme testovací data pro aktuálního uživatele
            user = User.query.get(user_id)
            create_transactions(user, num_months=3)
            create_budgets(user)
            create_savings_goals(user)

            # Přepočítáme data
            spending_by_category = calculate_monthly_spending_by_category(user_id)
            income_vs_expenses = calculate_income_vs_expenses(user_id)
        except Exception as e:
            # V případě chyby pokračujeme s prázdnými daty
            pass

    # Získání nejnovějšího skóre finančního zdraví
    health_score = FinancialHealthScore.query.filter_by(user_id=user_id).order_by(FinancialHealthScore.calculated_at.desc()).first()

    # Pokud skóre neexistuje, vypočítáme ho
    if not health_score:
        try:
            score = calculate_financial_health_score(user_id)
        except Exception:
            score = 0
    else:
        score = health_score.score

    # Získání doporučení
    try:
        recommendations = get_all_recommendations(user_id)
    except Exception:
        recommendations = []

    # Získání posledních transakcí
    recent_transactions = Transaction.query.filter_by(user_id=user_id).order_by(Transaction.transaction_date.desc()).limit(5).all()

    # Získání spořících cílů
    goals = SavingsGoal.query.filter_by(user_id=user_id).all()

    return render_template(
        'dashboard.html',
        spending=spending_by_category,
        finances=income_vs_expenses,
        score=score,
        recommendations=recommendations,
        transactions=recent_transactions,
        goals=goals
    )

@main.route('/transactions')
@login_required
def transactions():
    user_id = session.get('user_id')
    transactions = Transaction.query.filter_by(user_id=user_id).order_by(Transaction.transaction_date.desc()).all()
    categories = Category.query.all()
    return render_template('transactions.html', transactions=transactions, categories=categories, datetime=datetime)

@main.route('/add-transaction', methods=['POST'])
@login_required
def add_transaction():
    user_id = session.get('user_id')
    amount = float(request.form.get('amount'))
    description = request.form.get('description')
    category = request.form.get('category')
    transaction_date = datetime.strptime(request.form.get('date'), '%Y-%m-%d')
    transaction_type = 'expense' if amount < 0 else 'income'

    # Pokud není zadána kategorie, zkusíme ji automaticky kategorizovat
    if not category:
        category = categorize_transaction(description, amount)

    transaction = Transaction(
        user_id=user_id,
        amount=amount,
        description=description,
        category=category,
        transaction_date=transaction_date,
        transaction_type=transaction_type,
        currency='CZK'  # Výchozí měna
    )

    db.session.add(transaction)
    db.session.commit()

    flash('Transakce byla úspěšně přidána')
    return redirect(url_for('main.transactions'))

@main.route('/budget')
@login_required
def budget():
    user_id = session.get('user_id')
    budgets = Budget.query.filter_by(user_id=user_id).all()
    spending = calculate_monthly_spending_by_category(user_id)
    categories = Category.query.all()

    # Výpočet využití rozpočtu
    budget_usage = {}
    for budget in budgets:
        if budget.category in spending:
            usage = (spending[budget.category] / budget.amount) * 100
            budget_usage[budget.category] = min(usage, 100)  # Omezení na 100% pro zobrazení
        else:
            budget_usage[budget.category] = 0

    return render_template('budget.html', budgets=budgets, spending=spending, usage=budget_usage, categories=categories)

@main.route('/add-budget', methods=['POST'])
@login_required
def add_budget():
    user_id = session.get('user_id')
    category = request.form.get('category')
    amount = float(request.form.get('amount'))
    period = request.form.get('period', 'monthly')

    # Kontrola, zda již existuje rozpočet pro tuto kategorii
    existing = Budget.query.filter_by(user_id=user_id, category=category).first()
    if existing:
        existing.amount = amount
        existing.period = period
        db.session.commit()
        flash('Rozpočet byl úspěšně aktualizován')
    else:
        budget = Budget(
            user_id=user_id,
            category=category,
            amount=amount,
            period=period
        )
        db.session.add(budget)
        db.session.commit()
        flash('Rozpočet byl úspěšně přidán')

    return redirect(url_for('main.budget'))

@main.route('/goals')
@login_required
def goals():
    user_id = session.get('user_id')
    goals = SavingsGoal.query.filter_by(user_id=user_id).all()
    return render_template('goals.html', goals=goals, datetime=datetime)

@main.route('/add-goal', methods=['POST'])
@login_required
def add_goal():
    user_id = session.get('user_id')
    name = request.form.get('name')
    target_amount = float(request.form.get('target_amount'))
    current_amount = float(request.form.get('current_amount', 0))
    target_date = datetime.strptime(request.form.get('target_date'), '%Y-%m-%d') if request.form.get('target_date') else None

    goal = SavingsGoal(
        user_id=user_id,
        name=name,
        target_amount=target_amount,
        current_amount=current_amount,
        target_date=target_date
    )

    db.session.add(goal)
    db.session.commit()

    flash('Spořící cíl byl úspěšně přidán')
    return redirect(url_for('main.goals'))

@main.route('/update-goal/<int:goal_id>', methods=['POST'])
@login_required
def update_goal(goal_id):
    user_id = session.get('user_id')
    goal = SavingsGoal.query.filter_by(id=goal_id, user_id=user_id).first_or_404()

    current_amount = float(request.form.get('current_amount', 0))
    goal.current_amount = current_amount

    db.session.commit()

    flash('Spořící cíl byl úspěšně aktualizován')
    return redirect(url_for('main.goals'))

@main.route('/profile')
@login_required
def profile():
    user_id = session.get('user_id')
    user = User.query.get(user_id)
    return render_template('profile.html', user=user)

@main.route('/set-api-key', methods=['POST'])
@login_required
def set_api_key():
    api_key = request.form['api_key']
    session['api_key'] = api_key
    return jsonify({"status": "success"})

@main.route('/import-transactions')
@login_required
def import_transactions():
    user_id = session.get('user_id')
    api_key = session.get('api_key')

    if not api_key:
        flash('API klíč není nastaven')
        return redirect(url_for('main.profile'))

    # Získání transakcí za poslední měsíc
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)

    headers = {
        'Authorization': f'Bearer {api_key}'
    }

    try:
        response = requests.get(
            'https://api.revolut.com/api/1.0/transactions',
            headers=headers,
            params={
                'from': start_date.isoformat(),
                'to': end_date.isoformat()
            }
        )

        transactions_data = response.json()

        # Zpracování a uložení transakcí
        for tx_data in transactions_data:
            # Kontrola, zda transakce již existuje
            existing = Transaction.query.filter_by(
                user_id=user_id,
                transaction_date=datetime.fromisoformat(tx_data['created_at']),
                amount=tx_data['amount'],
                description=tx_data.get('description', '')
            ).first()

            if not existing:
                # Kategorizace transakce
                category = categorize_transaction(
                    tx_data.get('description', ''),
                    tx_data['amount']
                )

                transaction = Transaction(
                    user_id=user_id,
                    amount=tx_data['amount'],
                    currency=tx_data['currency'],
                    description=tx_data.get('description', ''),
                    category=category,
                    transaction_date=datetime.fromisoformat(tx_data['created_at']),
                    transaction_type=tx_data.get('type', 'expense' if tx_data['amount'] < 0 else 'income')
                )

                db.session.add(transaction)

        db.session.commit()
        flash('Transakce byly úspěšně importovány')

    except Exception as e:
        flash(f'Chyba při importu transakcí: {str(e)}')

    return redirect(url_for('main.transactions'))

# Registrace hlavního blueprintu
app.register_blueprint(main)

# Vytvoření databázových tabulek
@app.before_first_request
def create_tables():
    db.create_all()

    # Přidání výchozích kategorií, pokud neexistují
    categories = [
        ('potraviny', 'expense'),
        ('restaurace', 'expense'),
        ('doprava', 'expense'),
        ('energie', 'expense'),
        ('bydlení', 'expense'),
        ('zábava', 'expense'),
        ('nákupy', 'expense'),
        ('zdraví', 'expense'),
        ('vzdělávání', 'expense'),
        ('příjem', 'income'),
        ('úspory', 'transfer'),
        ('investice', 'transfer')
    ]

    for name, type in categories:
        if not Category.query.filter_by(name=name).first():
            category = Category(name=name, type=type)
            db.session.add(category)

    db.session.commit()

# Revolut API routes
@main.route('/revolut/setup', methods=['GET', 'POST'])
@login_required
def revolut_setup():
    """Nastavení Revolut integrace"""
    user_id = session.get('user_id')
    user = User.query.get(user_id)

    if request.method == 'POST':
        revolut_account_id = request.form.get('revolut_account_id')

        if revolut_account_id:
            if setup_revolut_for_user(user_id, revolut_account_id):
                flash('Revolut účet byl úspěšně nastaven!', 'success')
                return redirect(url_for('main.revolut_setup'))
            else:
                flash('Chyba při nastavování Revolut účtu.', 'error')
        else:
            flash('Prosím zadejte ID Revolut účtu.', 'error')

    # Získáme dostupné účty z Revolut API (pokud jsou credentials nastaveny)
    available_accounts = []
    try:
        revolut_service = RevolutSyncService()
        if revolut_service.test_connection():
            available_accounts = revolut_service.get_accounts()
    except Exception as e:
        flash(f'Nelze se připojit k Revolut API: {e}', 'warning')

    return render_template('revolut_setup.html',
                         user=user,
                         available_accounts=available_accounts)

@main.route('/revolut/sync', methods=['POST'])
@login_required
def revolut_sync():
    """Synchronizace transakcí z Revolut"""
    user_id = session.get('user_id')
    user = User.query.get(user_id)

    if not user.revolut_enabled or not user.revolut_account_id:
        flash('Revolut integrace není nastavena.', 'error')
        return redirect(url_for('main.revolut_setup'))

    try:
        revolut_service = RevolutSyncService()
        result = revolut_service.sync_user_transactions(user_id)

        if result['success']:
            # Aktualizujeme datum poslední synchronizace
            user.revolut_last_sync = datetime.now()
            db.session.commit()

            flash(f'Synchronizace dokončena! Nové transakce: {result["new_transactions"]}, '
                  f'aktualizované: {result["updated_transactions"]}, '
                  f'přeskočené: {result["skipped_transactions"]}', 'success')
        else:
            flash(f'Chyba při synchronizaci: {result["error"]}', 'error')

    except Exception as e:
        flash(f'Chyba při synchronizaci: {e}', 'error')

    return redirect(url_for('main.dashboard'))

@main.route('/revolut/test', methods=['POST'])
@login_required
def revolut_test():
    """Test připojení k Revolut API"""
    try:
        revolut_service = RevolutSyncService()
        if revolut_service.test_connection():
            flash('Připojení k Revolut API je funkční!', 'success')
        else:
            flash('Nelze se připojit k Revolut API. Zkontrolujte nastavení.', 'error')
    except Exception as e:
        flash(f'Chyba při testování připojení: {e}', 'error')

    return redirect(url_for('main.revolut_setup'))

# Tato část je pro lokální vývoj
if __name__ == '__main__':
    # Spustíme aplikaci v debug módu pro snadnější vývoj
    app.run(debug=True)

# Tato část je pro PythonAnywhere nebo jiné hostingové služby
# application = app