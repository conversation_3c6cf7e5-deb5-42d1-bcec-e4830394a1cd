"""
Revolut API integrace pro získávání transakcí
"""
import os
import jwt
import time
import requests
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import json


class RevolutAPI:
    """
    Třída pro práci s Revolut Business API
    """
    
    def __init__(self):
        self.base_url = "https://b2b.revolut.com/api/1.0"
        self.sandbox_url = "https://sandbox-b2b.revolut.com/api/1.0"
        self.is_sandbox = os.getenv('REVOLUT_SANDBOX', 'true').lower() == 'true'

        # API credentials z environment variables
        self.client_id = os.getenv('REVOLUT_CLIENT_ID')
        self.private_key = os.getenv('REVOLUT_PRIVATE_KEY')
        self.access_token = None
        self.refresh_token = None
        self.token_expires_at = None

        # Demo režim pro testování bez skutečného API
        self.demo_mode = (self.client_id == 'demo_client_id' or
                         not self.client_id or
                         not self.private_key)
        
    def get_base_url(self) -> str:
        """Vrátí správnou base URL podle prostředí"""
        return self.sandbox_url if self.is_sandbox else self.base_url
    
    def create_jwt_assertion(self) -> str:
        """
        Vytvoří JWT assertion pro autentifikaci
        """
        if not self.client_id or not self.private_key:
            raise ValueError("REVOLUT_CLIENT_ID a REVOLUT_PRIVATE_KEY musí být nastaveny")
        
        # Payload pro JWT
        payload = {
            'iss': self.client_id,  # issuer
            'sub': self.client_id,  # subject
            'aud': 'https://revolut.com',  # audience
            'iat': int(time.time()),  # issued at
            'exp': int(time.time()) + 3600  # expires in 1 hour
        }
        
        # Vytvoření JWT tokenu
        try:
            # Private key může být buď string nebo cesta k souboru
            if self.private_key.startswith('-----BEGIN'):
                private_key = self.private_key
            else:
                # Pokud je to cesta k souboru
                with open(self.private_key, 'r') as f:
                    private_key = f.read()
            
            token = jwt.encode(payload, private_key, algorithm='RS256')
            return token
        except Exception as e:
            raise ValueError(f"Chyba při vytváření JWT tokenu: {e}")
    
    def get_access_token(self) -> str:
        """
        Získá access token pro API volání
        """
        # Pokud máme platný token, vrátíme ho
        if (self.access_token and self.token_expires_at and 
            datetime.now() < self.token_expires_at - timedelta(minutes=5)):
            return self.access_token
        
        # Pokud máme refresh token, zkusíme ho použít
        if self.refresh_token:
            try:
                return self.refresh_access_token()
            except:
                pass  # Pokud refresh selže, pokračujeme s novým tokenem
        
        # Vytvoříme nový token
        return self.create_new_access_token()
    
    def create_new_access_token(self) -> str:
        """
        Vytvoří nový access token pomocí JWT assertion
        """
        jwt_assertion = self.create_jwt_assertion()
        
        url = f"{self.get_base_url()}/auth/token"
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        data = {
            'grant_type': 'urn:ietf:params:oauth:grant-type:jwt-bearer',
            'assertion': jwt_assertion,
            'scope': 'READ'  # Pouze čtení pro získávání transakcí
        }
        
        response = requests.post(url, headers=headers, data=data)
        
        if response.status_code == 200:
            token_data = response.json()
            self.access_token = token_data['access_token']
            self.refresh_token = token_data.get('refresh_token')
            
            # Nastavíme expiraci (40 minut podle dokumentace)
            expires_in = token_data.get('expires_in', 2400)  # 40 minut
            self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)
            
            return self.access_token
        else:
            raise Exception(f"Chyba při získávání access tokenu: {response.status_code} - {response.text}")
    
    def refresh_access_token(self) -> str:
        """
        Obnoví access token pomocí refresh tokenu
        """
        if not self.refresh_token:
            raise ValueError("Refresh token není k dispozici")
        
        url = f"{self.get_base_url()}/auth/token"
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        data = {
            'grant_type': 'refresh_token',
            'refresh_token': self.refresh_token
        }
        
        response = requests.post(url, headers=headers, data=data)
        
        if response.status_code == 200:
            token_data = response.json()
            self.access_token = token_data['access_token']
            self.refresh_token = token_data.get('refresh_token', self.refresh_token)
            
            # Nastavíme expiraci
            expires_in = token_data.get('expires_in', 2400)
            self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)
            
            return self.access_token
        else:
            raise Exception(f"Chyba při obnovování access tokenu: {response.status_code} - {response.text}")
    
    def get_accounts(self) -> List[Dict]:
        """
        Získá seznam účtů
        """
        if self.demo_mode:
            return self._get_demo_accounts()

        access_token = self.get_access_token()

        url = f"{self.get_base_url()}/accounts"
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }

        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Chyba při získávání účtů: {response.status_code} - {response.text}")

    def _get_demo_accounts(self) -> List[Dict]:
        """
        Vrátí demo účty pro testování
        """
        return [
            {
                'id': 'demo-account-czk',
                'name': 'Demo CZK účet',
                'currency': 'CZK',
                'balance': 25000.50,
                'state': 'active'
            },
            {
                'id': 'demo-account-eur',
                'name': 'Demo EUR účet',
                'currency': 'EUR',
                'balance': 1200.75,
                'state': 'active'
            }
        ]
    
    def get_transactions(self, account_id: Optional[str] = None,
                        from_date: Optional[datetime] = None,
                        to_date: Optional[datetime] = None,
                        count: int = 100) -> List[Dict]:
        """
        Získá transakce z Revolut API

        Args:
            account_id: ID účtu (volitelné)
            from_date: Datum od (volitelné)
            to_date: Datum do (volitelné)
            count: Počet transakcí (max 1000)

        Returns:
            Seznam transakcí
        """
        if self.demo_mode:
            return self._get_demo_transactions(account_id, from_date, to_date, count)

        access_token = self.get_access_token()

        url = f"{self.get_base_url()}/transactions"
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }

        # Parametry dotazu
        params = {
            'count': min(count, 1000)  # Max 1000 podle API
        }

        if account_id:
            params['account'] = account_id

        if from_date:
            params['from'] = from_date.isoformat()

        if to_date:
            params['to'] = to_date.isoformat()

        response = requests.get(url, headers=headers, params=params)

        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Chyba při získávání transakcí: {response.status_code} - {response.text}")

    def _get_demo_transactions(self, account_id: Optional[str] = None,
                              from_date: Optional[datetime] = None,
                              to_date: Optional[datetime] = None,
                              count: int = 100) -> List[Dict]:
        """
        Vrátí demo transakce pro testování
        """
        import random

        # Generujeme demo transakce
        demo_transactions = []

        # Pokud není zadáno datum od, použijeme posledních 30 dní
        if not from_date:
            from_date = datetime.now() - timedelta(days=30)

        if not to_date:
            to_date = datetime.now()

        # Generujeme náhodné transakce
        merchants = [
            {'name': 'Tesco', 'category_code': '5411'},
            {'name': 'McDonald\'s', 'category_code': '5814'},
            {'name': 'Shell', 'category_code': '5541'},
            {'name': 'H&M', 'category_code': '5651'},
            {'name': 'Alza.cz', 'category_code': '5732'},
            {'name': 'Lékárna', 'category_code': '5912'},
            {'name': 'Kino', 'category_code': '7832'},
            {'name': 'Uber', 'category_code': '4121'},
        ]

        for i in range(min(count, 20)):  # Max 20 demo transakcí
            # Náhodné datum v rozsahu
            days_diff = (to_date - from_date).days
            random_days = random.randint(0, max(1, days_diff))
            transaction_date = from_date + timedelta(days=random_days)

            # Náhodný merchant
            merchant = random.choice(merchants)

            # Náhodná částka (většinou výdaje)
            if random.random() < 0.9:  # 90% výdajů
                amount = -random.randint(50, 2000)
            else:  # 10% příjmů
                amount = random.randint(1000, 5000)

            transaction = {
                'id': f'demo-tx-{i+1:03d}',
                'type': 'card_payment' if amount < 0 else 'transfer',
                'state': 'completed',
                'created_at': transaction_date.isoformat() + 'Z',
                'completed_at': transaction_date.isoformat() + 'Z',
                'reference': f'Demo transakce {i+1}',
                'legs': [{
                    'account': {'id': account_id or 'demo-account-czk'},
                    'amount': amount,
                    'currency': 'CZK'
                }],
                'merchant': merchant if amount < 0 else None
            }

            demo_transactions.append(transaction)

        return demo_transactions
    
    def test_connection(self) -> bool:
        """
        Otestuje připojení k Revolut API
        """
        try:
            if self.demo_mode:
                print("Demo režim: Test připojení úspěšný")
                return True

            accounts = self.get_accounts()
            return True
        except Exception as e:
            print(f"Test připojení selhal: {e}")
            return False


def map_revolut_transaction_to_local(revolut_transaction: Dict, user_id: int) -> Dict:
    """
    Mapuje transakci z Revolut API na lokální formát
    
    Args:
        revolut_transaction: Transakce z Revolut API
        user_id: ID uživatele v lokální databázi
    
    Returns:
        Transakce v lokálním formátu
    """
    # Získáme první leg transakce (většinou je jen jeden)
    leg = revolut_transaction['legs'][0] if revolut_transaction['legs'] else {}
    
    # Mapování typu transakce
    transaction_type = revolut_transaction.get('type', 'transfer')
    
    # Mapování kategorie podle typu a merchant informací
    category = map_revolut_category(revolut_transaction)
    
    # Částka (záporná pro výdaje, kladná pro příjmy)
    amount = leg.get('amount', 0)
    
    # Popis transakce
    description = revolut_transaction.get('reference', '')
    if revolut_transaction.get('merchant'):
        merchant_name = revolut_transaction['merchant'].get('name', '')
        if merchant_name:
            description = merchant_name
    
    return {
        'user_id': user_id,
        'amount': amount,
        'category': category,
        'description': description,
        'transaction_date': datetime.fromisoformat(revolut_transaction['created_at'].replace('Z', '+00:00')),
        'external_id': revolut_transaction['id'],  # Pro identifikaci duplikátů
        'source': 'revolut'
    }


def map_revolut_category(revolut_transaction: Dict) -> str:
    """
    Mapuje kategorii z Revolut transakce na lokální kategorie
    """
    transaction_type = revolut_transaction.get('type', '')
    merchant = revolut_transaction.get('merchant', {})
    merchant_category = merchant.get('category_code', '') if merchant else ''
    
    # Mapování podle typu transakce
    if transaction_type == 'card_payment':
        # Mapování podle merchant category code (MCC)
        mcc_mapping = {
            '5411': 'potraviny',      # Grocery stores
            '5812': 'restaurace',     # Eating places
            '5541': 'doprava',        # Service stations
            '5732': 'elektronika',    # Electronics stores
            '5651': 'oblečení',       # Family clothing stores
            '5999': 'ostatní',        # Miscellaneous retail
            '6011': 'ostatní',        # ATM
            '4121': 'doprava',        # Taxicabs
            '5814': 'restaurace',     # Fast food restaurants
            '5912': 'zdraví',         # Drug stores
            '5943': 'ostatní',        # Stationery stores
            '5311': 'ostatní',        # Department stores
            '5200': 'domácnost',      # Home supply warehouse stores
            '5722': 'domácnost',      # Household appliance stores
            '5945': 'ostatní',        # Hobby, toy, and game shops
            '5941': 'sport',          # Sporting goods stores
            '5942': 'ostatní',        # Book stores
            '7832': 'zábava',         # Motion picture theaters
            '7991': 'zábava',         # Tourist attractions
            '8220': 'vzdělávání',     # Colleges, universities
            '8299': 'vzdělávání',     # Schools and educational services
            '5499': 'potraviny',      # Miscellaneous food stores
            '5661': 'oblečení',       # Shoe stores
            '5691': 'oblečení',       # Men's and women's clothing stores
            '5699': 'oblečení',       # Miscellaneous apparel stores
            '5712': 'domácnost',      # Furniture, home furnishings
            '5713': 'domácnost',      # Floor covering stores
            '5714': 'domácnost',      # Drapery, window covering stores
            '5718': 'domácnost',      # Fireplaces, fireplace screens
            '5719': 'domácnost',      # Miscellaneous home furnishing stores
            '5921': 'ostatní',        # Package stores - beer, wine, liquor
            '5931': 'ostatní',        # Used merchandise stores
            '5932': 'ostatní',        # Antique shops
            '5933': 'ostatní',        # Pawn shops
            '5935': 'ostatní',        # Wrecking and salvage yards
            '5937': 'ostatní',        # Antique reproductions
            '5940': 'ostatní',        # Bicycle shops
            '5944': 'ostatní',        # Jewelry stores, watches, clocks
            '5946': 'ostatní',        # Camera and photographic supply stores
            '5947': 'dárky',          # Gift, card, novelty, souvenir shops
            '5948': 'ostatní',        # Luggage and leather goods stores
            '5949': 'ostatní',        # Sewing, needlework, fabric stores
            '5950': 'ostatní',        # Glassware, crystal stores
            '5960': 'ostatní',        # Direct marketing - insurance services
            '5962': 'ostatní',        # Direct marketing - travel
            '5963': 'ostatní',        # Door-to-door sales
            '5964': 'ostatní',        # Direct marketing - catalog merchant
            '5965': 'ostatní',        # Direct marketing - combination catalog and retail
            '5966': 'ostatní',        # Direct marketing - outbound telemarketing
            '5967': 'ostatní',        # Direct marketing - inbound telemarketing
            '5968': 'ostatní',        # Direct marketing - continuity/subscription
            '5969': 'ostatní',        # Direct marketing - not elsewhere classified
            '5970': 'ostatní',        # Artist's supply and craft shops
            '5971': 'ostatní',        # Art dealers and galleries
            '5972': 'ostatní',        # Stamp and coin stores
            '5973': 'ostatní',        # Religious goods stores
            '5975': 'ostatní',        # Hearing aids sales and supplies
            '5976': 'zdraví',         # Orthopedic goods - prosthetic devices
            '5977': 'zdraví',         # Cosmetic stores
            '5978': 'ostatní',        # Typewriter stores
            '5983': 'doprava',        # Fuel dealers (non-automotive)
            '5992': 'ostatní',        # Florists
            '5993': 'ostatní',        # Cigar stores and stands
            '5994': 'ostatní',        # News dealers and newsstands
            '5995': 'ostatní',        # Pet shops, pet food, supplies
            '5996': 'ostatní',        # Swimming pools sales
            '5997': 'elektronika',    # Electric razor stores
            '5998': 'ostatní',        # Tent and awning shops
        }
        
        if merchant_category in mcc_mapping:
            return mcc_mapping[merchant_category]
        else:
            return 'ostatní'
    
    elif transaction_type == 'atm':
        return 'ostatní'
    elif transaction_type == 'transfer':
        return 'ostatní'
    elif transaction_type == 'topup':
        return 'ostatní'
    elif transaction_type == 'fee':
        return 'ostatní'
    else:
        return 'ostatní'
