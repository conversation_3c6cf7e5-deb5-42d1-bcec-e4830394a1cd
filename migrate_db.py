"""
Skript pro migraci databáze - přidání Revolut sloupců
"""
from app import app, db
from models import User, Transaction
import sqlite3

def migrate_database():
    """
    Přidá nové sloupce do existující databáze
    """
    with app.app_context():
        # Získáme cestu k databázi
        db_path = app.config['SQLALCHEMY_DATABASE_URI'].replace('sqlite:///', '')
        
        # Připojíme se k databázi
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        try:
            # Přidáme sloupce do tabulky User
            print("Přidávám sloupce do tabulky User...")
            
            # Zkontrolujeme, zda sloupce již existují
            cursor.execute("PRAGMA table_info(user)")
            columns = [column[1] for column in cursor.fetchall()]
            
            if 'revolut_account_id' not in columns:
                cursor.execute("ALTER TABLE user ADD COLUMN revolut_account_id VARCHAR(120)")
                print("✓ Přidán sloupec revolut_account_id")
            
            if 'revolut_last_sync' not in columns:
                cursor.execute("ALTER TABLE user ADD COLUMN revolut_last_sync DATETIME")
                print("✓ Přidán sloupec revolut_last_sync")
            
            if 'revolut_enabled' not in columns:
                cursor.execute("ALTER TABLE user ADD COLUMN revolut_enabled BOOLEAN DEFAULT 0")
                print("✓ Přidán sloupec revolut_enabled")
            
            # Přidáme sloupce do tabulky Transaction
            print("\nPřidávám sloupce do tabulky Transaction...")
            
            cursor.execute("PRAGMA table_info(transaction)")
            columns = [column[1] for column in cursor.fetchall()]
            
            if 'external_id' not in columns:
                cursor.execute("ALTER TABLE transaction ADD COLUMN external_id VARCHAR(120)")
                print("✓ Přidán sloupec external_id")
            
            if 'source' not in columns:
                cursor.execute("ALTER TABLE transaction ADD COLUMN source VARCHAR(50) DEFAULT 'manual'")
                print("✓ Přidán sloupec source")
            
            if 'completed_at' not in columns:
                cursor.execute("ALTER TABLE transaction ADD COLUMN completed_at DATETIME")
                print("✓ Přidán sloupec completed_at")
            
            # Aktualizujeme existující transakce, aby měly source = 'manual'
            cursor.execute("UPDATE transaction SET source = 'manual' WHERE source IS NULL")
            print("✓ Aktualizovány existující transakce")
            
            # Uložíme změny
            conn.commit()
            print("\n✅ Migrace databáze byla úspěšně dokončena!")
            
        except Exception as e:
            print(f"❌ Chyba při migraci: {e}")
            conn.rollback()
            raise
        finally:
            conn.close()

if __name__ == '__main__':
    print("🔄 Spouštím migraci databáze...")
    migrate_database()
    print("✅ Migrace dokončena!")
