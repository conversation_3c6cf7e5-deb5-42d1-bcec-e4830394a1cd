"""
Synchronizace transakcí z Revolut API
"""
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from models import db, Transaction, User
from revolut_api import RevolutAPI, map_revolut_transaction_to_local
import logging

# Nastavení loggingu
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RevolutSyncService:
    """
    Služba pro synchronizaci transakcí z Revolut API
    """
    
    def __init__(self):
        self.revolut_api = RevolutAPI()
    
    def test_connection(self) -> bool:
        """
        Otestuje připojení k Revolut API
        """
        return self.revolut_api.test_connection()
    
    def sync_user_transactions(self, user_id: int, days_back: int = 30) -> Dict:
        """
        Synchronizuje transakce pro konkrétního uživatele
        
        Args:
            user_id: ID uživatele
            days_back: Počet dní zpět pro synchronizaci
        
        Returns:
            Slovník s výsledky synchronizace
        """
        try:
            # Získáme uživatele
            user = User.query.get(user_id)
            if not user:
                return {'success': False, 'error': 'Uživatel nenalezen'}
            
            # Zkontrolujeme, zda má uživatel nastavené Revolut credentials
            if not user.revolut_account_id:
                return {'success': False, 'error': 'Uživatel nemá nastavený Revolut účet'}
            
            # Datum od kterého synchronizujeme
            from_date = datetime.now() - timedelta(days=days_back)
            
            # Získáme transakce z Revolut API
            revolut_transactions = self.revolut_api.get_transactions(
                account_id=user.revolut_account_id,
                from_date=from_date,
                count=1000
            )
            
            # Zpracujeme transakce
            result = self.process_transactions(revolut_transactions, user_id)
            
            logger.info(f"Synchronizace dokončena pro uživatele {user_id}: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Chyba při synchronizaci transakcí pro uživatele {user_id}: {e}")
            return {'success': False, 'error': str(e)}
    
    def process_transactions(self, revolut_transactions: List[Dict], user_id: int) -> Dict:
        """
        Zpracuje transakce z Revolut API a uloží je do databáze
        
        Args:
            revolut_transactions: Seznam transakcí z Revolut API
            user_id: ID uživatele
        
        Returns:
            Slovník s výsledky zpracování
        """
        new_transactions = 0
        updated_transactions = 0
        skipped_transactions = 0
        errors = []
        
        for revolut_transaction in revolut_transactions:
            try:
                # Zkontrolujeme, zda transakce již existuje
                external_id = revolut_transaction['id']
                existing_transaction = Transaction.query.filter_by(
                    user_id=user_id,
                    external_id=external_id
                ).first()
                
                if existing_transaction:
                    # Transakce již existuje, zkontrolujeme, zda se změnila
                    if self.should_update_transaction(existing_transaction, revolut_transaction):
                        self.update_transaction(existing_transaction, revolut_transaction)
                        updated_transactions += 1
                    else:
                        skipped_transactions += 1
                else:
                    # Nová transakce
                    self.create_transaction(revolut_transaction, user_id)
                    new_transactions += 1
                    
            except Exception as e:
                error_msg = f"Chyba při zpracování transakce {revolut_transaction.get('id', 'unknown')}: {e}"
                logger.error(error_msg)
                errors.append(error_msg)
        
        # Uložíme změny do databáze
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            error_msg = f"Chyba při ukládání do databáze: {e}"
            logger.error(error_msg)
            errors.append(error_msg)
            return {'success': False, 'error': error_msg}
        
        return {
            'success': True,
            'new_transactions': new_transactions,
            'updated_transactions': updated_transactions,
            'skipped_transactions': skipped_transactions,
            'total_processed': len(revolut_transactions),
            'errors': errors
        }
    
    def should_update_transaction(self, existing_transaction: Transaction, revolut_transaction: Dict) -> bool:
        """
        Zkontroluje, zda by měla být existující transakce aktualizována
        
        Args:
            existing_transaction: Existující transakce v databázi
            revolut_transaction: Transakce z Revolut API
        
        Returns:
            True pokud by měla být transakce aktualizována
        """
        # Zkontrolujeme stav transakce
        revolut_state = revolut_transaction.get('state', '')
        
        # Pokud se stav změnil z pending na completed, aktualizujeme
        if (existing_transaction.description.find('pending') != -1 and 
            revolut_state == 'completed'):
            return True
        
        # Pokud se změnilo datum dokončení
        completed_at = revolut_transaction.get('completed_at')
        if completed_at and not existing_transaction.completed_at:
            return True
        
        return False
    
    def update_transaction(self, existing_transaction: Transaction, revolut_transaction: Dict):
        """
        Aktualizuje existující transakci
        
        Args:
            existing_transaction: Existující transakce v databázi
            revolut_transaction: Transakce z Revolut API
        """
        # Aktualizujeme stav
        state = revolut_transaction.get('state', '')
        if state:
            existing_transaction.description = f"{existing_transaction.description} ({state})"
        
        # Aktualizujeme datum dokončení
        completed_at = revolut_transaction.get('completed_at')
        if completed_at:
            existing_transaction.completed_at = datetime.fromisoformat(completed_at.replace('Z', '+00:00'))
        
        logger.info(f"Aktualizována transakce {existing_transaction.id}")
    
    def create_transaction(self, revolut_transaction: Dict, user_id: int):
        """
        Vytvoří novou transakci v databázi
        
        Args:
            revolut_transaction: Transakce z Revolut API
            user_id: ID uživatele
        """
        # Mapujeme transakci na lokální formát
        local_transaction_data = map_revolut_transaction_to_local(revolut_transaction, user_id)
        
        # Vytvoříme novou transakci
        transaction = Transaction(
            user_id=local_transaction_data['user_id'],
            amount=local_transaction_data['amount'],
            category=local_transaction_data['category'],
            description=local_transaction_data['description'],
            transaction_date=local_transaction_data['transaction_date'],
            external_id=local_transaction_data['external_id'],
            source=local_transaction_data['source']
        )
        
        # Přidáme datum dokončení pokud existuje
        completed_at = revolut_transaction.get('completed_at')
        if completed_at:
            transaction.completed_at = datetime.fromisoformat(completed_at.replace('Z', '+00:00'))
        
        db.session.add(transaction)
        logger.info(f"Vytvořena nová transakce z Revolut: {transaction.description}")
    
    def get_accounts(self) -> List[Dict]:
        """
        Získá seznam účtů z Revolut API
        """
        try:
            return self.revolut_api.get_accounts()
        except Exception as e:
            logger.error(f"Chyba při získávání účtů: {e}")
            return []
    
    def sync_all_users(self, days_back: int = 30) -> Dict:
        """
        Synchronizuje transakce pro všechny uživatele s nastaveným Revolut účtem
        
        Args:
            days_back: Počet dní zpět pro synchronizaci
        
        Returns:
            Slovník s výsledky synchronizace
        """
        users_with_revolut = User.query.filter(User.revolut_account_id.isnot(None)).all()
        
        total_results = {
            'success': True,
            'users_processed': 0,
            'total_new_transactions': 0,
            'total_updated_transactions': 0,
            'total_skipped_transactions': 0,
            'errors': []
        }
        
        for user in users_with_revolut:
            try:
                result = self.sync_user_transactions(user.id, days_back)
                
                if result['success']:
                    total_results['users_processed'] += 1
                    total_results['total_new_transactions'] += result.get('new_transactions', 0)
                    total_results['total_updated_transactions'] += result.get('updated_transactions', 0)
                    total_results['total_skipped_transactions'] += result.get('skipped_transactions', 0)
                    total_results['errors'].extend(result.get('errors', []))
                else:
                    total_results['errors'].append(f"Uživatel {user.id}: {result.get('error', 'Neznámá chyba')}")
                    
            except Exception as e:
                error_msg = f"Chyba při synchronizaci uživatele {user.id}: {e}"
                logger.error(error_msg)
                total_results['errors'].append(error_msg)
        
        if total_results['errors']:
            total_results['success'] = len(total_results['errors']) < len(users_with_revolut)
        
        logger.info(f"Hromadná synchronizace dokončena: {total_results}")
        return total_results


def setup_revolut_for_user(user_id: int, revolut_account_id: str) -> bool:
    """
    Nastaví Revolut účet pro uživatele
    
    Args:
        user_id: ID uživatele
        revolut_account_id: ID Revolut účtu
    
    Returns:
        True pokud bylo nastavení úspěšné
    """
    try:
        user = User.query.get(user_id)
        if not user:
            return False
        
        user.revolut_account_id = revolut_account_id
        db.session.commit()
        
        logger.info(f"Revolut účet nastaven pro uživatele {user_id}")
        return True
        
    except Exception as e:
        logger.error(f"Chyba při nastavování Revolut účtu pro uživatele {user_id}: {e}")
        db.session.rollback()
        return False
