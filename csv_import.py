"""
Import transakcí z CSV souboru (např. z Revolut exportu)
"""
import csv
import os
from datetime import datetime
from models import db, Transaction, User
import logging

logger = logging.getLogger(__name__)

def import_revolut_csv(user_id: int, csv_file_path: str) -> dict:
    """
    Importuje transakce z Revolut CSV souboru
    
    Args:
        user_id: ID uživatele
        csv_file_path: Cesta k CSV souboru
    
    Returns:
        Slovník s výsledky importu
    """
    if not os.path.exists(csv_file_path):
        return {'success': False, 'error': f'Soubor {csv_file_path} neexistuje'}
    
    user = User.query.get(user_id)
    if not user:
        return {'success': False, 'error': 'Uživatel nenalezen'}
    
    new_transactions = 0
    updated_transactions = 0
    skipped_transactions = 0
    errors = []
    
    try:
        with open(csv_file_path, 'r', encoding='utf-8') as file:
            # <PERSON><PERSON><PERSON><PERSON> různé formáty CSV
            sample = file.read(1024)
            file.seek(0)
            
            # Detekce oddělovače
            sniffer = csv.Sniffer()
            delimiter = sniffer.sniff(sample).delimiter
            
            reader = csv.DictReader(file, delimiter=delimiter)
            
            for row_num, row in enumerate(reader, 1):
                try:
                    # Mapování podle různých formátů Revolut CSV
                    transaction_data = map_csv_row_to_transaction(row, user_id)
                    
                    if not transaction_data:
                        skipped_transactions += 1
                        continue
                    
                    # Zkontrolujeme, zda transakce již existuje
                    existing = Transaction.query.filter_by(
                        user_id=user_id,
                        external_id=transaction_data['external_id']
                    ).first()
                    
                    if existing:
                        # Aktualizujeme existující transakci
                        for key, value in transaction_data.items():
                            if key != 'user_id' and key != 'external_id':
                                setattr(existing, key, value)
                        updated_transactions += 1
                    else:
                        # Vytvoříme novou transakci
                        transaction = Transaction(**transaction_data)
                        db.session.add(transaction)
                        new_transactions += 1
                        
                except Exception as e:
                    error_msg = f"Řádek {row_num}: {str(e)}"
                    logger.error(error_msg)
                    errors.append(error_msg)
        
        # Uložíme změny
        db.session.commit()
        
        # Označíme uživatele jako mající Revolut data
        user.revolut_ob_enabled = True
        user.revolut_ob_last_sync = datetime.now()
        db.session.commit()
        
        result = {
            'success': True,
            'new_transactions': new_transactions,
            'updated_transactions': updated_transactions,
            'skipped_transactions': skipped_transactions,
            'total_processed': new_transactions + updated_transactions + skipped_transactions,
            'errors': errors
        }
        
        logger.info(f"CSV import dokončen pro uživatele {user_id}: {result}")
        return result
        
    except Exception as e:
        db.session.rollback()
        error_msg = f"Chyba při čtení CSV souboru: {str(e)}"
        logger.error(error_msg)
        return {'success': False, 'error': error_msg}

def map_csv_row_to_transaction(row: dict, user_id: int) -> dict:
    """
    Mapuje řádek CSV na transakci
    
    Podporuje různé formáty Revolut CSV exportu
    """
    # Normalizujeme klíče (odstraníme mezery, převedeme na lowercase)
    normalized_row = {k.strip().lower().replace(' ', '_'): v for k, v in row.items()}
    
    # Různé možné názvy sloupců v Revolut CSV
    date_fields = ['date', 'completed_date', 'started_date', 'transaction_date']
    amount_fields = ['amount', 'total_amount', 'fee_amount']
    description_fields = ['description', 'reference', 'merchant', 'counterpart']
    currency_fields = ['currency', 'base_currency']
    
    # Najdeme správné pole pro datum
    transaction_date = None
    for field in date_fields:
        if field in normalized_row and normalized_row[field]:
            try:
                # Zkusíme různé formáty data
                date_str = normalized_row[field]
                for date_format in ['%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%Y-%m-%d %H:%M:%S']:
                    try:
                        transaction_date = datetime.strptime(date_str, date_format)
                        break
                    except ValueError:
                        continue
                if transaction_date:
                    break
            except:
                continue
    
    if not transaction_date:
        return None
    
    # Najdeme částku
    amount = 0
    for field in amount_fields:
        if field in normalized_row and normalized_row[field]:
            try:
                amount_str = normalized_row[field].replace(',', '').replace(' ', '')
                # Odstraníme měnu z částky
                amount_str = ''.join(c for c in amount_str if c.isdigit() or c in '.-')
                amount = float(amount_str)
                break
            except:
                continue
    
    # Najdeme popis
    description = 'Imported transaction'
    for field in description_fields:
        if field in normalized_row and normalized_row[field]:
            description = normalized_row[field]
            break
    
    # Najdeme měnu
    currency = 'CZK'
    for field in currency_fields:
        if field in normalized_row and normalized_row[field]:
            currency = normalized_row[field]
            break
    
    # Určíme typ transakce
    transaction_type = 'expense' if amount < 0 else 'income'
    
    # Mapování kategorie
    category = map_description_to_category(description)
    
    # Vytvoříme externí ID
    external_id = f"csv_import_{user_id}_{transaction_date.strftime('%Y%m%d')}_{abs(hash(description + str(amount)))}"
    
    return {
        'user_id': user_id,
        'amount': amount,
        'category': category,
        'description': description,
        'transaction_date': transaction_date,
        'external_id': external_id,
        'source': 'csv_import',
        'currency': currency,
        'transaction_type': transaction_type
    }

def map_description_to_category(description: str) -> str:
    """
    Mapuje popis transakce na kategorii
    """
    description_lower = description.lower()
    
    # Mapování podle klíčových slov
    if any(word in description_lower for word in ['tesco', 'albert', 'billa', 'lidl', 'kaufland', 'grocery', 'food']):
        return 'potraviny'
    elif any(word in description_lower for word in ['mcdonald', 'kfc', 'restaurant', 'pizza', 'burger']):
        return 'restaurace'
    elif any(word in description_lower for word in ['shell', 'benzina', 'mol', 'uber', 'taxi', 'transport', 'fuel']):
        return 'doprava'
    elif any(word in description_lower for word in ['h&m', 'zara', 'reserved', 'oblečení', 'clothing']):
        return 'oblečení'
    elif any(word in description_lower for word in ['alza', 'czc', 'mall', 'amazon', 'electronics']):
        return 'elektronika'
    elif any(word in description_lower for word in ['lékárna', 'pharmacy', 'zdraví', 'health', 'medical']):
        return 'zdraví'
    elif any(word in description_lower for word in ['kino', 'cinema', 'netflix', 'spotify', 'entertainment']):
        return 'zábava'
    elif any(word in description_lower for word in ['salary', 'wage', 'income', 'plat', 'mzda']):
        return 'příjem'
    else:
        return 'ostatní'
